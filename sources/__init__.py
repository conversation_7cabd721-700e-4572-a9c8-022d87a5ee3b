"""
Sources package for ailex-be-ingest.

Contains integrations with various legal data sources.
"""

# Lazy imports to avoid dependency issues
def __getattr__(name):
    if name in ["JustelClient", "JustelIngester", "JustelParser", "JustelNormalizer"]:
        from .justel import __getattr__ as justel_getattr
        return justel_getattr(name)
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    "JustelClient",
    "JustelIngester",
    "JustelParser",
    "JustelNormalizer"
]
