"""
Dutch language patterns for Justel documents.

Contains CSS selectors, regex patterns, and text markers for parsing
Dutch Belgian legal documents.
"""

# CSS selectors for document structure
SELECTORS = {
    # Document metadata
    "title": "h1.title, .document-title, h1",
    "numac": ".numac, .document-number",
    "publication_date": ".publication-date, .datum-publicatie",
    "eli": ".eli-identifier",
    
    # Document structure
    "toc_selector": ".table-of-contents, .inhoudsopgave, .inhoudstafel",
    "content_selector": ".document-content, .volledige-tekst, .document-inhoud",
    "article_container": ".artikel, .art",
    "chapter_container": ".hoofdstuk, .chapter",
    "section_container": ".afdeling, .section",
    
    # Navigation and pagination
    "next_page": ".volgende-pagina, .next-page",
    "prev_page": ".vorige-pagina, .prev-page",
    "page_info": ".pagina-info, .pagination-info"
}

# Regex patterns for text extraction
PATTERNS = {
    # Article identification
    "article_pattern": r"Art(?:ikel)?\.\s*(\d+(?:\.\d+)*(?:[a-z]+)?)",
    "article_range": r"Art(?:ikel)?s?\.\s*(\d+)\s*(?:tot|-)?\s*(\d+)",
    
    # Structural elements
    "heading_pattern": r"HOOFDSTUK\s+([IVX]+|[0-9]+)(?:\s*[.-]\s*(.+))?",
    "section_pattern": r"Afdeling\s+([IVX]+|\d+)(?:\s*[.-]\s*(.+))?",
    "subsection_pattern": r"Onderafdeling\s+([IVX]+|\d+)(?:\s*[.-]\s*(.+))?",
    "title_pattern": r"TITEL\s+([IVX]+|\d+)(?:\s*[.-]\s*(.+))?",
    
    # Legal references
    "numac_reference": r"NUMAC\s*:?\s*(\d{4}[A-Z]\d{5})",
    "eli_reference": r"ELI\s*:?\s*(\/eli\/[^\s]+)",
    "law_reference": r"wet\s+van\s+(\d{1,2})\s+(\w+)\s+(\d{4})",
    "decree_reference": r"(?:besluit|decreet)\s+van\s+(\d{1,2})\s+(\w+)\s+(\d{4})",
    
    # Dates
    "date_pattern": r"(\d{1,2})\s+(januari|februari|maart|april|mei|juni|juli|augustus|september|oktober|november|december)\s+(\d{4})",
    "date_short": r"(\d{1,2})\/(\d{1,2})\/(\d{4})",
    
    # Amendment markers
    "amendment_intro": r"(?:gewijzigd|vervangen|aangevuld|ingevoegd)\s+bij",
    "repeal_intro": r"(?:opgeheven|weggelaten|ingetrokken)\s+bij"
}

# Text markers for legal status
MARKERS = {
    "repeal_markers": [
        "opgeheven", "weggelaten", "ingetrokken", "zonder voorwerp",
        "niet van toepassing", "vervallen", "verlopen"
    ],
    "amendment_markers": [
        "gewijzigd bij", "vervangen bij", "aangevuld bij",
        "ingevoegd bij", "gesubstitueerd bij", "geamendeerd bij"
    ],
    "entry_force_markers": [
        "treedt in werking", "wordt van kracht", "van toepassing vanaf",
        "van kracht op", "vanaf"
    ],
    "suspension_markers": [
        "geschorst", "uitgesteld", "opgeschort", "in afwachting"
    ]
}

# Month name mappings for date parsing
MONTH_NAMES = {
    "januari": 1, "februari": 2, "maart": 3, "april": 4,
    "mei": 5, "juni": 6, "juli": 7, "augustus": 8,
    "september": 9, "oktober": 10, "november": 11, "december": 12
}

# Document type indicators
DOCUMENT_TYPES = {
    "wet": ["wet", "wetboek"],
    "koninklijk_besluit": ["koninklijk besluit", "k.b.", "kb"],
    "ministerieel_besluit": ["ministerieel besluit", "m.b.", "mb"],
    "decreet": ["decreet"],
    "ordonnantie": ["ordonnantie"],
    "reglement": ["reglement", "huishoudelijk reglement"]
}

# Common abbreviations and their expansions
ABBREVIATIONS = {
    "art.": "artikel",
    "al.": "alinea",
    "§": "paragraaf",
    "hfdst.": "hoofdstuk",
    "afd.": "afdeling",
    "tit.": "titel",
    "cf.": "confer",
    "enz.": "enzovoort",
    "bv.": "bijvoorbeeld"
}

# Text cleaning patterns
CLEANUP_PATTERNS = {
    # Remove extra whitespace
    "whitespace": r"\s+",
    # Remove page numbers
    "page_numbers": r"Pagina\s+\d+\s+van\s+\d+",
    # Remove footer text
    "footer": r"(?:Moniteur Belge|Belgisch Staatsblad).*?(?:\d{2}\/\d{2}\/\d{4}|\d{4})",
    # Remove header text
    "header": r"^.*?(?:NUMAC|ELI).*?$",
    # Normalize punctuation
    "punctuation": r"([.!?])\s*([A-Z])"
}
