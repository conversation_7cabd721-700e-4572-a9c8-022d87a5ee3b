"""
Justel-specific data models and constants.

Contains NUMAC/ELI types and validation patterns for Belgian legal documents.
"""

import re
from datetime import date
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from common.models import NUMAC_PATTERN, ELI_PATTERN_BE


class NumacDocument(BaseModel):
    """Model for documents identified by NUMAC."""
    
    numac: str = Field(..., description="NUMAC identifier (e.g., 2022A30600)")
    title: str = Field(..., description="Document title")
    publication_date: date = Field(..., description="Publication date")
    language: str = Field(..., description="Language code (fr, nl)")
    document_type: str = Field(..., description="Type of document (loi, arrete, etc.)")
    eli: Optional[str] = Field(None, description="ELI identifier if available")
    
    @validator('numac')
    def validate_numac(cls, v):
        if not re.match(NUMAC_PATTERN, v):
            raise ValueError(f"Invalid NUMAC format: {v}")
        return v
    
    @validator('language')
    def validate_language(cls, v):
        if v not in ['fr', 'nl']:
            raise ValueError(f"Language must be 'fr' or 'nl', got: {v}")
        return v


class EliDocument(BaseModel):
    """Model for documents identified by ELI."""
    
    eli: str = Field(..., description="ELI identifier")
    numac: Optional[str] = Field(None, description="NUMAC if available")
    title: str = Field(..., description="Document title")
    publication_date: date = Field(..., description="Publication date")
    language: str = Field(..., description="Language code (fr, nl)")
    document_type: str = Field(..., description="Type extracted from ELI")
    
    @validator('eli')
    def validate_eli(cls, v):
        if not re.match(ELI_PATTERN_BE, v):
            raise ValueError(f"Invalid Belgian ELI format: {v}")
        return v
    
    @validator('language')
    def validate_language(cls, v):
        if v not in ['fr', 'nl']:
            raise ValueError(f"Language must be 'fr' or 'nl', got: {v}")
        return v


class JustelSearchResult(BaseModel):
    """Model for search results from Justel."""
    
    documents: List[NumacDocument] = Field(default_factory=list)
    total_found: int = Field(0, description="Total documents found")
    page: int = Field(1, description="Current page")
    has_more: bool = Field(False, description="More pages available")


# Document type mappings
DOCUMENT_TYPE_MAPPING = {
    'fr': {
        'loi': 'law',
        'arrêté royal': 'royal_decree',
        'arrêté ministériel': 'ministerial_order',
        'décret': 'decree',
        'ordonnance': 'ordinance'
    },
    'nl': {
        'wet': 'law',
        'koninklijk besluit': 'royal_decree',
        'ministerieel besluit': 'ministerial_order',
        'decreet': 'decree',
        'ordonnantie': 'ordinance'
    }
}

# Legal status indicators
LEGAL_STATUS_INDICATORS = {
    'fr': {
        'active': ['en vigueur', 'applicable'],
        'repealed': ['abrogé', 'supprimé', 'retiré'],
        'amended': ['modifié', 'remplacé', 'complété']
    },
    'nl': {
        'active': ['van kracht', 'van toepassing'],
        'repealed': ['opgeheven', 'weggelaten', 'ingetrokken'],
        'amended': ['gewijzigd', 'vervangen', 'aangevuld']
    }
}

# Court hierarchy for future case law integration
COURT_HIERARCHY = {
    'fr': {
        'cour_de_cassation': 'Supreme Court',
        'cour_d_appel': 'Court of Appeal',
        'tribunal_de_premiere_instance': 'First Instance Court',
        'tribunal_de_police': 'Police Court',
        'conseil_d_etat': 'Council of State'
    },
    'nl': {
        'hof_van_cassatie': 'Supreme Court',
        'hof_van_beroep': 'Court of Appeal',
        'rechtbank_van_eerste_aanleg': 'First Instance Court',
        'politierechtbank': 'Police Court',
        'raad_van_state': 'Council of State'
    }
}
