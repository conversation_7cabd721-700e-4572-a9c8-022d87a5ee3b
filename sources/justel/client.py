"""
Justel client for ailex-be-ingest.

Handles web scraping of Belgium's Justel legal database with proper
rate limiting, robots.txt respect, and error handling.
"""

import time
import logging
import sqlite3
from datetime import datetime, date
from typing import Iterator, Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
from pathlib import Path

import requests
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from bs4 import BeautifulSoup

from common.config import config

logger = logging.getLogger(__name__)


class JustelClient:
    """Client for the Justel legal database."""
    
    def __init__(self, 
                 base_url_fr: Optional[str] = None,
                 base_url_nl: Optional[str] = None,
                 rate_limit: Optional[float] = None,
                 respect_robots: bool = True):
        """
        Initialize the Justel client.
        
        Args:
            base_url_fr: Base URL for French documents
            base_url_nl: Base URL for Dutch documents
            rate_limit: Rate limit in requests per second
            respect_robots: Whether to respect robots.txt
        """
        self.base_url_fr = base_url_fr or config.JUSTEL_BASE_URL_FR
        self.base_url_nl = base_url_nl or config.JUSTEL_BASE_URL_NL
        self.rate_limit = rate_limit or config.JUSTEL_RATE_LIMIT_RPS
        self.respect_robots = respect_robots and config.JUSTEL_RESPECT_ROBOTS
        
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": config.USER_AGENT,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "fr,nl,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        })
        
        self.last_request_time = 0.0
        self._robots_checked = False
        self._robots_allowed = True
        
        # Initialize checkpoint database
        self._init_checkpoint_db()
    
    def _init_checkpoint_db(self):
        """Initialize SQLite checkpoint database."""
        db_path = Path(config.CHECKPOINT_DB)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS visited_urls (
                    url TEXT PRIMARY KEY,
                    visited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status_code INTEGER,
                    content_length INTEGER
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS document_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numac TEXT,
                    eli TEXT,
                    language TEXT,
                    url TEXT,
                    priority INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_at TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS processing_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action TEXT,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def _check_robots_txt(self, base_url: str) -> bool:
        """Check robots.txt for crawling permissions."""
        if not self.respect_robots or self._robots_checked:
            return self._robots_allowed
        
        try:
            robots_url = urljoin(base_url, '/robots.txt')
            response = self.session.get(robots_url, timeout=10)
            
            if response.status_code == 200:
                # Simple robots.txt parsing - look for disallow rules
                content = response.text.lower()
                user_agent_section = False
                
                for line in content.split('\n'):
                    line = line.strip()
                    if line.startswith('user-agent:'):
                        agent = line.split(':', 1)[1].strip()
                        user_agent_section = agent == '*' or 'ailex' in agent
                    elif user_agent_section and line.startswith('disallow:'):
                        path = line.split(':', 1)[1].strip()
                        if path == '/' or '/eli/' in path:
                            logger.warning(f"Robots.txt disallows crawling: {path}")
                            self._robots_allowed = False
                            break
            
            self._robots_checked = True
            return self._robots_allowed
            
        except Exception as e:
            logger.warning(f"Could not check robots.txt: {e}")
            self._robots_checked = True
            return True  # Assume allowed if can't check
    
    def _rate_limit_wait(self):
        """Enforce rate limiting between requests."""
        if self.rate_limit > 0:
            time_since_last = time.time() - self.last_request_time
            min_interval = 1.0 / self.rate_limit
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=2, max=60),
        retry=retry_if_exception_type((requests.exceptions.RequestException, requests.exceptions.Timeout))
    )
    def _make_request(self, url: str) -> requests.Response:
        """
        Make a rate-limited request with retry logic.
        
        Args:
            url: URL to request
            
        Returns:
            Response object
            
        Raises:
            requests.exceptions.RequestException: On persistent errors
        """
        # Check robots.txt
        base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
        if not self._check_robots_txt(base_url):
            raise requests.exceptions.RequestException(f"Robots.txt disallows crawling: {url}")
        
        self._rate_limit_wait()
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Log successful request
            self._log_visit(url, response.status_code, len(response.content))
            
            return response
            
        except requests.exceptions.HTTPError as e:
            if response.status_code == 429:
                logger.warning("Rate limit hit, retrying...")
                raise
            elif response.status_code >= 500:
                logger.warning(f"Server error {response.status_code}, retrying...")
                raise
            else:
                logger.error(f"HTTP error {response.status_code}: {e}")
                raise
        except requests.exceptions.RequestException as e:
            logger.warning(f"Request failed: {e}")
            raise
    
    def _log_visit(self, url: str, status_code: int, content_length: int):
        """Log visited URL to checkpoint database."""
        try:
            with sqlite3.connect(config.CHECKPOINT_DB) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO visited_urls 
                    (url, visited_at, status_code, content_length)
                    VALUES (?, ?, ?, ?)
                """, (url, datetime.now(), status_code, content_length))
                conn.commit()
        except Exception as e:
            logger.warning(f"Could not log visit: {e}")
    
    def get_document_by_numac(self, numac: str, language: str = "fr") -> Optional[str]:
        """
        Fetch a document by NUMAC identifier.
        
        Args:
            numac: NUMAC identifier (e.g., 2022A30600)
            language: Language code (fr or nl)
            
        Returns:
            HTML content or None if not found
        """
        base_url = self.base_url_fr if language == "fr" else self.base_url_nl
        
        # Construct URL based on NUMAC pattern
        # This is a simplified approach - real implementation would need
        # to handle Justel's specific URL structure
        url = f"{base_url}/{numac}"
        
        try:
            response = self._make_request(url)
            return response.text
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                logger.warning(f"Document {numac} not found")
                return None
            raise
    
    def search_documents(self, 
                        query: str = "",
                        language: str = "fr",
                        document_type: Optional[str] = None,
                        date_from: Optional[date] = None,
                        date_to: Optional[date] = None,
                        limit: Optional[int] = None) -> Iterator[Dict[str, Any]]:
        """
        Search for documents in Justel.
        
        Args:
            query: Search query
            language: Language code (fr or nl)
            document_type: Type filter (loi, arrete, etc.)
            date_from: Start date filter
            date_to: End date filter
            limit: Maximum number of results
            
        Yields:
            Document metadata dictionaries
        """
        # This is a placeholder implementation
        # Real implementation would need to handle Justel's search interface
        logger.warning("Document search not yet implemented - placeholder")
        return iter([])
    
    def get_recent_documents(self, 
                           language: str = "fr",
                           days: int = 30,
                           limit: Optional[int] = None) -> Iterator[Dict[str, Any]]:
        """
        Get recently published documents.
        
        Args:
            language: Language code (fr or nl)
            days: Number of days to look back
            limit: Maximum number of results
            
        Yields:
            Document metadata dictionaries
        """
        # This is a placeholder implementation
        logger.warning("Recent documents not yet implemented - placeholder")
        return iter([])
    
    def close(self):
        """Close the HTTP session."""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
