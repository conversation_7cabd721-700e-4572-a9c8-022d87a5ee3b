"""
Justel integration for ailex-be-ingest.

This module provides integration with Belgium's Justel legal database,
focusing on consolidated legislation (v1 scope).
"""

# Lazy imports to avoid dependency issues
def __getattr__(name):
    if name == "JustelClient":
        from .client import JustelClient
        return JustelClient
    elif name == "JustelIngester":
        from .ingest import JustelIngester
        return JustelIngester
    elif name == "JustelParser":
        from .parse import JustelParser
        return JustelParser
    elif name == "JustelNormalizer":
        from .normalize import JustelNormalizer
        return JustelNormalizer
    elif name == "NumacDocument":
        from .models import NumacDocument
        return NumacDocument
    elif name == "EliDocument":
        from .models import EliDocument
        return EliDocument
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    "JustelClient",
    "JustelIngester",
    "JustelParser",
    "JustelNormalizer",
    "NumacDocument",
    "EliDocument"
]
