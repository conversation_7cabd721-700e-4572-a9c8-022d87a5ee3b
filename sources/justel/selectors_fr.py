"""
French language patterns for Justel documents.

Contains CSS selectors, regex patterns, and text markers for parsing
French Belgian legal documents.
"""

# CSS selectors for document structure
SELECTORS = {
    # Document metadata
    "title": "h1.title, .document-title, h1",
    "numac": ".numac, .document-number",
    "publication_date": ".publication-date, .date-publication",
    "eli": ".eli-identifier",
    
    # Document structure
    "toc_selector": ".table-of-contents, .sommaire, .table-matieres",
    "content_selector": ".document-content, .texte-integral, .contenu-document",
    "article_container": ".article, .art",
    "chapter_container": ".chapitre, .chapter",
    "section_container": ".section",
    
    # Navigation and pagination
    "next_page": ".page-suivante, .next-page",
    "prev_page": ".page-precedente, .prev-page",
    "page_info": ".page-info, .pagination-info"
}

# Regex patterns for text extraction
PATTERNS = {
    # Article identification
    "article_pattern": r"Art(?:icle)?\.\s*(\d+(?:\.\d+)*(?:[a-z]+)?)",
    "article_range": r"Art(?:icle)?s?\.\s*(\d+)\s*(?:à|-)?\s*(\d+)",
    
    # Structural elements
    "heading_pattern": r"CHAPITRE\s+([IVX]+|[0-9]+)(?:\s*[.-]\s*(.+))?",
    "section_pattern": r"Section\s+([IVX]+|\d+)(?:\s*[.-]\s*(.+))?",
    "subsection_pattern": r"Sous-section\s+([IVX]+|\d+)(?:\s*[.-]\s*(.+))?",
    "title_pattern": r"TITRE\s+([IVX]+|\d+)(?:\s*[.-]\s*(.+))?",
    
    # Legal references
    "numac_reference": r"NUMAC\s*:?\s*(\d{4}[A-Z]\d{5})",
    "eli_reference": r"ELI\s*:?\s*(\/eli\/[^\s]+)",
    "law_reference": r"loi\s+du\s+(\d{1,2})\s+(\w+)\s+(\d{4})",
    "decree_reference": r"(?:arrêté|décret)\s+du\s+(\d{1,2})\s+(\w+)\s+(\d{4})",
    
    # Dates
    "date_pattern": r"(\d{1,2})\s+(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+(\d{4})",
    "date_short": r"(\d{1,2})\/(\d{1,2})\/(\d{4})",
    
    # Amendment markers
    "amendment_intro": r"(?:modifié|remplacé|complété|inséré)\s+par",
    "repeal_intro": r"(?:abrogé|supprimé|retiré)\s+par"
}

# Text markers for legal status
MARKERS = {
    "repeal_markers": [
        "abrogé", "supprimé", "retiré", "sans objet",
        "non applicable", "caduc", "périmé"
    ],
    "amendment_markers": [
        "modifié par", "remplacé par", "complété par",
        "inséré par", "substitué par", "amendé par"
    ],
    "entry_force_markers": [
        "entre en vigueur", "prend effet", "applicable à partir",
        "en vigueur le", "à dater du"
    ],
    "suspension_markers": [
        "suspendu", "différé", "reporté", "en attente"
    ]
}

# Month name mappings for date parsing
MONTH_NAMES = {
    "janvier": 1, "février": 2, "mars": 3, "avril": 4,
    "mai": 5, "juin": 6, "juillet": 7, "août": 8,
    "septembre": 9, "octobre": 10, "novembre": 11, "décembre": 12
}

# Document type indicators
DOCUMENT_TYPES = {
    "loi": ["loi", "code"],
    "arrete_royal": ["arrêté royal", "a.r.", "ar"],
    "arrete_ministeriel": ["arrêté ministériel", "a.m.", "am"],
    "decret": ["décret"],
    "ordonnance": ["ordonnance"],
    "reglement": ["règlement", "règlement d'ordre intérieur"]
}

# Common abbreviations and their expansions
ABBREVIATIONS = {
    "art.": "article",
    "al.": "alinéa", 
    "§": "paragraphe",
    "ch.": "chapitre",
    "sect.": "section",
    "tit.": "titre",
    "cf.": "confer",
    "etc.": "et cetera",
    "p.ex.": "par exemple"
}

# Text cleaning patterns
CLEANUP_PATTERNS = {
    # Remove extra whitespace
    "whitespace": r"\s+",
    # Remove page numbers
    "page_numbers": r"Page\s+\d+\s+sur\s+\d+",
    # Remove footer text
    "footer": r"(?:Moniteur Belge|Belgisch Staatsblad).*?(?:\d{2}\/\d{2}\/\d{4}|\d{4})",
    # Remove header text
    "header": r"^.*?(?:NUMAC|ELI).*?$",
    # Normalize punctuation
    "punctuation": r"([.!?])\s*([A-Z])"
}
