"""
Justel normalizer for ailex-be-ingest.

Converts parsed Justel HTML documents to CommonAct/CommonArticle format
for consistent processing across all data sources.
"""

import logging
from datetime import datetime, date
from typing import List, Tuple, Dict, Any, Optional

from common.models import CommonAct, CommonArticle
from .parse import JustelParser
from .models import NumacDocument, EliDocument

logger = logging.getLogger(__name__)


class JustelNormalizer:
    """Convert Justel HTML to CommonAct/CommonArticle format."""
    
    def __init__(self, language: str = "fr"):
        """
        Initialize normalizer for specific language.
        
        Args:
            language: Language code (fr or nl)
        """
        self.language = language
        self.parser = JustelParser(language)
    
    def normalize_document(self, html: str, identifier: str) -> Tuple[Optional[CommonAct], List[CommonArticle]]:
        """
        Convert HTML document to CommonAct + CommonArticle list.
        
        Args:
            html: HTML content of the document
            identifier: Document identifier (NUMAC or ELI)
            
        Returns:
            Tuple of (CommonAct, List[CommonArticle]) or (None, []) if parsing fails
        """
        try:
            # Parse the HTML document
            parsed_doc = self.parser.parse_document(html)
            if not parsed_doc:
                logger.warning(f"Failed to parse document {identifier}")
                return None, []
            
            # Extract act metadata
            act = self._create_common_act(parsed_doc, identifier)
            if not act:
                logger.warning(f"Failed to create CommonAct for {identifier}")
                return None, []
            
            # Extract articles
            articles = self._create_common_articles(parsed_doc, act.id)
            
            logger.info(f"Normalized document {identifier}: 1 act, {len(articles)} articles")
            return act, articles
            
        except Exception as e:
            logger.error(f"Error normalizing document {identifier}: {e}")
            return None, []
    
    def _create_common_act(self, parsed_doc: Dict[str, Any], identifier: str) -> Optional[CommonAct]:
        """Create CommonAct from parsed document data."""
        metadata = parsed_doc.get("metadata", {})
        
        # Extract required fields
        title = metadata.get("title")
        if not title:
            logger.warning(f"No title found for document {identifier}")
            return None
        
        # Parse publication date
        pub_date_str = metadata.get("publication_date")
        if pub_date_str:
            try:
                pub_date = date.fromisoformat(pub_date_str)
            except ValueError:
                logger.warning(f"Invalid publication date format: {pub_date_str}")
                pub_date = date.today()  # Fallback
        else:
            pub_date = date.today()  # Fallback
        
        # Determine document ID and ELI
        doc_id = identifier
        eli = metadata.get("eli")
        
        # If identifier is ELI, use it; if NUMAC, try to get ELI from metadata
        if identifier.startswith("/eli/"):
            eli = identifier
            doc_id = metadata.get("numac", identifier)
        else:
            doc_id = identifier  # NUMAC
        
        # Create source-specific metadata
        source_metadata = {
            "numac": metadata.get("numac"),
            "eli": eli,
            "document_type": metadata.get("document_type"),
            "structure": parsed_doc.get("structure", {}),
            "references": parsed_doc.get("references", []),
            "parsed_at": parsed_doc.get("parsed_at"),
            "language": self.language
        }
        
        # Remove None values
        source_metadata = {k: v for k, v in source_metadata.items() if v is not None}
        
        try:
            return CommonAct(
                id=doc_id,
                title=title,
                date=pub_date,
                language=self.language,
                source="justel_consolidated",
                eli=eli,
                metadata=source_metadata
            )
        except Exception as e:
            logger.error(f"Error creating CommonAct: {e}")
            return None
    
    def _create_common_articles(self, parsed_doc: Dict[str, Any], act_id: str) -> List[CommonArticle]:
        """Create CommonArticle list from parsed document data."""
        articles = []
        parsed_articles = parsed_doc.get("articles", [])
        
        for i, article_data in enumerate(parsed_articles):
            try:
                article = self._create_common_article(article_data, act_id, i)
                if article:
                    articles.append(article)
            except Exception as e:
                logger.warning(f"Error creating article {i} for act {act_id}: {e}")
                continue
        
        return articles
    
    def _create_common_article(self, article_data: Dict[str, Any], act_id: str, index: int) -> Optional[CommonArticle]:
        """Create a single CommonArticle from article data."""
        article_number = article_data.get("number")
        article_text = article_data.get("text")
        
        if not article_number or not article_text:
            logger.warning(f"Missing article number or text for article {index}")
            return None
        
        # Create article ID
        article_id = f"{act_id}:art_{article_number}"
        
        # Extract heading if available (first line if it looks like a heading)
        lines = article_text.split('\n')
        heading = None
        text_content = article_text
        
        if len(lines) > 1:
            first_line = lines[0].strip()
            # Simple heuristic: if first line is short and doesn't end with period, it might be a heading
            if len(first_line) < 100 and not first_line.endswith('.'):
                heading = first_line
                text_content = '\n'.join(lines[1:]).strip()
        
        # Create article metadata
        article_metadata = {
            "html": article_data.get("html", ""),
            "index": index,
            "source_number": article_number
        }
        
        try:
            return CommonArticle(
                id=article_id,
                act_id=act_id,
                number=article_number,
                heading=heading,
                text=text_content,
                language=self.language,
                metadata=article_metadata
            )
        except Exception as e:
            logger.error(f"Error creating CommonArticle: {e}")
            return None
    
    def validate_normalized_document(self, act: CommonAct, articles: List[CommonArticle]) -> Dict[str, Any]:
        """
        Validate normalized document and return quality metrics.
        
        Args:
            act: The normalized act
            articles: List of normalized articles
            
        Returns:
            Dictionary with validation results and metrics
        """
        validation = {
            "valid": True,
            "warnings": [],
            "metrics": {},
            "errors": []
        }
        
        # Basic validation
        if not act:
            validation["valid"] = False
            validation["errors"].append("No act created")
            return validation
        
        if not articles:
            validation["warnings"].append("No articles extracted")
        
        # Metrics
        validation["metrics"] = {
            "article_count": len(articles),
            "avg_article_length": sum(len(a.text) for a in articles) / len(articles) if articles else 0,
            "has_eli": bool(act.eli),
            "has_numac": bool(act.metadata.get("numac")),
            "document_type": act.metadata.get("document_type"),
            "language": act.language
        }
        
        # Quality checks
        if len(articles) == 0:
            validation["warnings"].append("No articles found - document might be malformed")
        elif len(articles) < 3:
            validation["warnings"].append("Very few articles found - check parsing quality")
        
        # Check for very short articles (might indicate parsing issues)
        short_articles = [a for a in articles if len(a.text) < 50]
        if len(short_articles) > len(articles) * 0.3:  # More than 30% are very short
            validation["warnings"].append(f"{len(short_articles)} articles are very short - possible parsing issues")
        
        # Check for missing metadata
        if not act.metadata.get("document_type"):
            validation["warnings"].append("Document type not detected")
        
        if not act.eli and not act.metadata.get("numac"):
            validation["warnings"].append("Neither ELI nor NUMAC found")
        
        return validation
    
    def create_content_hash(self, text: str) -> int:
        """
        Create a stable hash for content comparison.
        
        Args:
            text: Text content to hash
            
        Returns:
            Integer hash value
        """
        # Normalize text for consistent hashing
        normalized = text.lower().strip()
        # Remove extra whitespace
        normalized = ' '.join(normalized.split())
        
        return hash(normalized)
    
    def extract_toc_count(self, parsed_doc: Dict[str, Any]) -> int:
        """
        Extract expected article count from table of contents.
        
        Args:
            parsed_doc: Parsed document data
            
        Returns:
            Expected article count from TOC, or 0 if not available
        """
        structure = parsed_doc.get("structure", {})
        toc = structure.get("toc", "")
        
        if not toc:
            return 0
        
        # Simple heuristic: count "Art." occurrences in TOC
        import re
        if self.language == "fr":
            pattern = r"Art\."
        else:
            pattern = r"Art\."
        
        matches = re.findall(pattern, toc, re.IGNORECASE)
        return len(matches)
