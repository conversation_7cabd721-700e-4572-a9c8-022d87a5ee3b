"""
Justel ingester for ailex-be-ingest.

Handles the complete ingestion pipeline for Belgian legal documents
from Justel, following the transactional pattern established by vla<PERSON>e_codex.
"""

import json
import logging
import sqlite3
from datetime import datetime, date
from pathlib import Path
from typing import Dict, Any, Optional, List

from common.config import config
from common.models import CommonAct, CommonArticle
from common.neo4j import Neo4jClient
from common.registry import GlobalRegistry
from .client import JustelClient
from .normalize import JustelNormalizer

logger = logging.getLogger(__name__)


class JustelIngester:
    """Ingester for Justel legal documents."""
    
    def __init__(self):
        """Initialize the Justel ingester."""
        self.client = JustelClient()
        self.normalizer_fr = JustelNormalizer("fr")
        self.normalizer_nl = JustelNormalizer("nl")
        self.neo4j = Neo4jClient()
        self.registry = GlobalRegistry()
        
        # Ensure output directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        dirs_to_create = [
            Path(config.OUT_DIR_RAW) / "consolidated" / "fr",
            Path(config.OUT_DIR_RAW) / "consolidated" / "nl",
            Path(config.OUT_DIR_PROCESSED) / "fr",
            Path(config.OUT_DIR_PROCESSED) / "nl",
            Path(config.LOGS_DIR)
        ]
        
        for dir_path in dirs_to_create:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def ingest_consolidated_law(self, 
                              limit: Optional[int] = None,
                              language: str = "fr",
                              dry_run: bool = False) -> Dict[str, Any]:
        """
        Ingest consolidated legislation from Justel.
        
        Args:
            limit: Maximum number of documents to process
            language: Language to process (fr or nl)
            dry_run: If True, don't make actual changes
            
        Returns:
            Dictionary with ingestion results
        """
        logger.info(f"Starting Justel consolidated law ingestion (language: {language}, limit: {limit})")
        
        results = {
            "processed": 0,
            "successful": 0,
            "failed": 0,
            "skipped": 0,
            "errors": []
        }
        
        try:
            # Get normalizer for language
            normalizer = self.normalizer_fr if language == "fr" else self.normalizer_nl
            
            # For now, this is a placeholder implementation
            # Real implementation would:
            # 1. Get document list from Justel
            # 2. Process each document
            # 3. Store results
            
            logger.warning("Justel ingestion not yet fully implemented - placeholder")
            
            # Placeholder: simulate processing a few documents
            sample_documents = self._get_sample_documents(language, limit or 5)
            
            for doc_info in sample_documents:
                try:
                    if dry_run:
                        logger.info(f"DRY RUN: Would process {doc_info['identifier']}")
                        results["processed"] += 1
                        results["successful"] += 1
                        continue
                    
                    # Process document
                    success = self._process_document(doc_info, normalizer, language)
                    
                    results["processed"] += 1
                    if success:
                        results["successful"] += 1
                    else:
                        results["failed"] += 1
                        
                except Exception as e:
                    logger.error(f"Error processing document {doc_info.get('identifier', 'unknown')}: {e}")
                    results["failed"] += 1
                    results["errors"].append(str(e))
            
            logger.info(f"Ingestion completed: {results['successful']}/{results['processed']} successful")
            return results
            
        except Exception as e:
            logger.error(f"Ingestion failed: {e}")
            results["errors"].append(str(e))
            return results
    
    def _get_sample_documents(self, language: str, limit: int) -> List[Dict[str, Any]]:
        """Get sample documents for testing (placeholder)."""
        # This is a placeholder - real implementation would query Justel
        sample_docs = []
        
        if language == "fr":
            sample_docs = [
                {"identifier": "2022A30600", "type": "loi", "title": "Loi relative à la protection des données"},
                {"identifier": "2023A40100", "type": "arrete_royal", "title": "Arrêté royal concernant la sécurité"},
                {"identifier": "2024A50200", "type": "decret", "title": "Décret sur l'environnement"}
            ]
        else:  # Dutch
            sample_docs = [
                {"identifier": "2022A30600", "type": "wet", "title": "Wet betreffende gegevensbescherming"},
                {"identifier": "2023A40100", "type": "koninklijk_besluit", "title": "Koninklijk besluit betreffende veiligheid"},
                {"identifier": "2024A50200", "type": "decreet", "title": "Decreet betreffende het milieu"}
            ]
        
        return sample_docs[:limit]
    
    def _process_document(self, doc_info: Dict[str, Any], normalizer: JustelNormalizer, language: str) -> bool:
        """
        Process a single document through the complete pipeline.
        
        Args:
            doc_info: Document information
            normalizer: Normalizer for the document language
            language: Document language
            
        Returns:
            True if successful, False otherwise
        """
        identifier = doc_info["identifier"]
        
        try:
            # Step 1: Fetch document HTML (placeholder)
            html_content = self._fetch_document_html(identifier, language)
            if not html_content:
                logger.warning(f"Could not fetch HTML for {identifier}")
                return False
            
            # Step 2: Save raw HTML
            raw_path = self._save_raw_html(identifier, language, html_content)
            
            # Step 3: Normalize to CommonAct/CommonArticle
            act, articles = normalizer.normalize_document(html_content, identifier)
            if not act:
                logger.warning(f"Could not normalize document {identifier}")
                return False
            
            # Step 4: Validate normalized document
            validation = normalizer.validate_normalized_document(act, articles)
            if not validation["valid"]:
                logger.error(f"Document validation failed for {identifier}: {validation['errors']}")
                return False
            
            # Step 5: Save processed JSON
            processed_path = self._save_processed_json(act, articles, language)
            
            # Step 6: Store in Neo4j (placeholder)
            self._store_in_neo4j(act, articles)
            
            # Step 7: Store in vector database (placeholder)
            self._store_in_vectors(act, articles)
            
            # Step 8: Update registry
            self._update_registry(act, raw_path, processed_path)
            
            logger.info(f"Successfully processed {identifier}: {len(articles)} articles")
            return True
            
        except Exception as e:
            logger.error(f"Error processing document {identifier}: {e}")
            return False
    
    def _fetch_document_html(self, identifier: str, language: str) -> Optional[str]:
        """Fetch document HTML from Justel (placeholder)."""
        # This is a placeholder - real implementation would use JustelClient
        logger.info(f"Fetching HTML for {identifier} ({language})")
        
        # Return sample HTML for testing
        return f"""
        <html>
        <head><title>Sample Document {identifier}</title></head>
        <body>
        <h1>Sample Law {identifier}</h1>
        <div class="numac">NUMAC: {identifier}</div>
        <div class="article">
        <p>Art. 1. This is article 1 of the sample document.</p>
        </div>
        <div class="article">
        <p>Art. 2. This is article 2 of the sample document.</p>
        </div>
        </body>
        </html>
        """
    
    def _save_raw_html(self, identifier: str, language: str, html_content: str) -> Path:
        """Save raw HTML content to file."""
        filename = f"{identifier}.{language}.html"
        file_path = Path(config.OUT_DIR_RAW) / "consolidated" / language / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.debug(f"Saved raw HTML to {file_path}")
        return file_path
    
    def _save_processed_json(self, act: CommonAct, articles: List[CommonArticle], language: str) -> Path:
        """Save processed document as JSON."""
        # Extract year from act date for directory structure
        year = act.date.year
        
        # Create year directory if it doesn't exist
        year_dir = Path(config.OUT_DIR_PROCESSED) / language / str(year)
        year_dir.mkdir(parents=True, exist_ok=True)
        
        # Save file
        filename = f"{act.id}.json"
        file_path = year_dir / filename
        
        data = {
            "act": act.dict(),
            "articles": [article.dict() for article in articles],
            "processed_at": datetime.now().isoformat()
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.debug(f"Saved processed JSON to {file_path}")
        return file_path
    
    def _store_in_neo4j(self, act: CommonAct, articles: List[CommonArticle]):
        """Store document in Neo4j (placeholder)."""
        logger.debug(f"Storing {act.id} in Neo4j (placeholder)")
        # Real implementation would use Neo4jClient
    
    def _store_in_vectors(self, act: CommonAct, articles: List[CommonArticle]):
        """Store document in vector database (placeholder)."""
        logger.debug(f"Storing {act.id} vectors (placeholder)")
        # Real implementation would create embeddings and store in Pinecone
    
    def _update_registry(self, act: CommonAct, raw_path: Path, processed_path: Path):
        """Update global registry (placeholder)."""
        logger.debug(f"Updating registry for {act.id} (placeholder)")
        # Real implementation would use GlobalRegistry
    
    def resume_ingestion(self, language: str = "fr") -> Dict[str, Any]:
        """Resume ingestion from checkpoint."""
        logger.info(f"Resuming Justel ingestion for {language}")
        
        # This is a placeholder - real implementation would:
        # 1. Read checkpoint from SQLite
        # 2. Continue from last processed document
        # 3. Update checkpoint as processing continues
        
        return self.ingest_consolidated_law(language=language)
    
    def get_checkpoint_status(self) -> Dict[str, Any]:
        """Get current checkpoint status."""
        checkpoint_path = Path(config.CHECKPOINT_DB)
        
        if not checkpoint_path.exists():
            return {"has_checkpoint": False}
        
        try:
            with sqlite3.connect(checkpoint_path) as conn:
                cursor = conn.execute("""
                    SELECT COUNT(*) as total, 
                           MAX(processed_at) as last_processed
                    FROM document_queue 
                    WHERE status = 'completed'
                """)
                row = cursor.fetchone()
                
                return {
                    "has_checkpoint": True,
                    "total_processed": row[0] if row else 0,
                    "last_processed": row[1] if row else None
                }
        except Exception as e:
            logger.error(f"Error reading checkpoint: {e}")
            return {"has_checkpoint": False, "error": str(e)}
    
    def close(self):
        """Close all connections."""
        if hasattr(self, 'client'):
            self.client.close()
        if hasattr(self, 'neo4j'):
            self.neo4j.close()
        if hasattr(self, 'registry'):
            self.registry.close()
