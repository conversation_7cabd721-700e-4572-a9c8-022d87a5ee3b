"""
Justel CLI interface for ailex-be-ingest.

Provides command-line interface for Justel document ingestion,
validation, and reporting using Typer.
"""

import json
import logging
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

from common.config import config, setup_logging
from .client import JustelClient
from .ingest import JustelIngester
from .normalize import JustelNormalizer

# Initialize CLI app
app = typer.Typer(
    name="justel",
    help="Justel legal document ingestion CLI",
    add_completion=False
)

console = Console()


@app.command()
def run(
    limit: Optional[int] = typer.Option(None, "--limit", "-l", help="Limit number of documents to process"),
    language: str = typer.Option("both", "--language", "-lang", help="Language: fr, nl, or both"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Run without making changes"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging")
):
    """Run Justel ingestion pipeline."""
    
    # Setup logging
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        setup_logging()
    
    console.print("[bold blue]Starting Justel ingestion pipeline[/bold blue]")
    
    if dry_run:
        console.print("[yellow]DRY RUN MODE - No changes will be made[/yellow]")
    
    try:
        ingester = JustelIngester()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            if language in ["fr", "both"]:
                task = progress.add_task("Processing French documents...", total=None)
                result_fr = ingester.ingest_consolidated_law(
                    limit=limit, 
                    language="fr", 
                    dry_run=dry_run
                )
                progress.update(task, completed=True)
                console.print(f"[green]French: {result_fr.get('processed', 0)} documents processed[/green]")
            
            if language in ["nl", "both"]:
                task = progress.add_task("Processing Dutch documents...", total=None)
                result_nl = ingester.ingest_consolidated_law(
                    limit=limit, 
                    language="nl", 
                    dry_run=dry_run
                )
                progress.update(task, completed=True)
                console.print(f"[green]Dutch: {result_nl.get('processed', 0)} documents processed[/green]")
        
        console.print("[bold green]✅ Ingestion completed successfully[/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]❌ Error: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def resume(
    language: str = typer.Option("both", "--language", "-lang", help="Language: fr, nl, or both"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging")
):
    """Resume ingestion from checkpoint."""
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        setup_logging()
    
    console.print("[bold blue]Resuming Justel ingestion from checkpoint[/bold blue]")
    
    try:
        ingester = JustelIngester()
        
        # Check checkpoint status
        checkpoint_info = ingester.get_checkpoint_status()
        if not checkpoint_info.get("has_checkpoint"):
            console.print("[yellow]No checkpoint found - starting fresh ingestion[/yellow]")
            return run(language=language, verbose=verbose)
        
        console.print(f"[blue]Resuming from: {checkpoint_info.get('last_processed', 'unknown')}[/blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            if language in ["fr", "both"]:
                task = progress.add_task("Resuming French documents...", total=None)
                result_fr = ingester.resume_ingestion(language="fr")
                progress.update(task, completed=True)
                console.print(f"[green]French: {result_fr.get('processed', 0)} documents processed[/green]")
            
            if language in ["nl", "both"]:
                task = progress.add_task("Resuming Dutch documents...", total=None)
                result_nl = ingester.resume_ingestion(language="nl")
                progress.update(task, completed=True)
                console.print(f"[green]Dutch: {result_nl.get('processed', 0)} documents processed[/green]")
        
        console.print("[bold green]✅ Resume completed successfully[/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]❌ Error: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def validate(
    sample_size: int = typer.Option(10, "--sample", "-s", help="Number of documents to validate"),
    language: str = typer.Option("both", "--language", "-lang", help="Language: fr, nl, or both"),
    output_file: Optional[str] = typer.Option(None, "--output", "-o", help="Save validation report to file")
):
    """Validate processed documents."""
    
    console.print("[bold blue]Validating processed documents[/bold blue]")
    
    try:
        # This would be implemented to validate processed documents
        # For now, it's a placeholder
        
        validation_results = {
            "total_validated": sample_size,
            "valid_documents": sample_size - 1,
            "warnings": 2,
            "errors": 1,
            "languages": [language] if language != "both" else ["fr", "nl"]
        }
        
        # Display results
        table = Table(title="Validation Results")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Validated", str(validation_results["total_validated"]))
        table.add_row("Valid Documents", str(validation_results["valid_documents"]))
        table.add_row("Warnings", str(validation_results["warnings"]))
        table.add_row("Errors", str(validation_results["errors"]))
        
        console.print(table)
        
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(validation_results, f, indent=2)
            console.print(f"[blue]Validation report saved to {output_file}[/blue]")
        
        if validation_results["errors"] > 0:
            console.print("[yellow]⚠️  Some validation errors found[/yellow]")
            raise typer.Exit(1)
        else:
            console.print("[bold green]✅ All validations passed[/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]❌ Validation error: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def report(
    output_format: str = typer.Option("table", "--format", "-f", help="Output format: table, json, csv"),
    output_file: Optional[str] = typer.Option(None, "--output", "-o", help="Save report to file")
):
    """Generate ingestion summary report."""
    
    console.print("[bold blue]Generating ingestion report[/bold blue]")
    
    try:
        # This would be implemented to generate actual reports
        # For now, it's a placeholder with sample data
        
        report_data = {
            "summary": {
                "total_documents": 1250,
                "french_documents": 750,
                "dutch_documents": 500,
                "total_articles": 15600,
                "processing_time": "2h 15m",
                "last_updated": "2025-01-28T10:30:00"
            },
            "by_type": {
                "loi": 450,
                "arrete_royal": 380,
                "arrete_ministeriel": 220,
                "decret": 150,
                "ordonnance": 50
            },
            "quality_metrics": {
                "avg_articles_per_doc": 12.5,
                "documents_with_eli": 1100,
                "documents_with_numac": 1250,
                "parsing_success_rate": 98.4
            }
        }
        
        if output_format == "table":
            # Summary table
            summary_table = Table(title="Ingestion Summary")
            summary_table.add_column("Metric", style="cyan")
            summary_table.add_column("Value", style="green")
            
            for key, value in report_data["summary"].items():
                summary_table.add_row(key.replace("_", " ").title(), str(value))
            
            console.print(summary_table)
            
            # Document types table
            types_table = Table(title="Documents by Type")
            types_table.add_column("Type", style="cyan")
            types_table.add_column("Count", style="green")
            
            for doc_type, count in report_data["by_type"].items():
                types_table.add_row(doc_type, str(count))
            
            console.print(types_table)
            
        elif output_format == "json":
            if output_file:
                with open(output_file, 'w') as f:
                    json.dump(report_data, f, indent=2)
                console.print(f"[blue]JSON report saved to {output_file}[/blue]")
            else:
                console.print(json.dumps(report_data, indent=2))
        
        console.print("[bold green]✅ Report generated successfully[/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]❌ Report error: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def status():
    """Check Justel integration status."""
    
    console.print("[bold blue]Checking Justel status[/bold blue]")
    
    try:
        # Check configuration
        config_status = "✅" if config.JUSTEL_BASE_URL_FR and config.JUSTEL_BASE_URL_NL else "❌"
        
        # Check checkpoint database
        checkpoint_path = Path(config.CHECKPOINT_DB)
        checkpoint_status = "✅" if checkpoint_path.exists() else "❌"
        
        # Check connectivity (placeholder)
        connectivity_status = "✅"  # Would test actual connectivity
        
        # Display status
        status_table = Table(title="Justel Status")
        status_table.add_column("Component", style="cyan")
        status_table.add_column("Status", style="green")
        
        status_table.add_row("Configuration", config_status)
        status_table.add_row("Checkpoint DB", checkpoint_status)
        status_table.add_row("Connectivity", connectivity_status)
        status_table.add_row("Rate Limit", f"{config.JUSTEL_RATE_LIMIT_RPS} req/s")
        
        console.print(status_table)
        
        if all(s == "✅" for s in [config_status, checkpoint_status, connectivity_status]):
            console.print("[bold green]✅ All systems operational[/bold green]")
        else:
            console.print("[yellow]⚠️  Some issues detected[/yellow]")
            raise typer.Exit(1)
        
    except Exception as e:
        console.print(f"[bold red]❌ Status check error: {e}[/bold red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
