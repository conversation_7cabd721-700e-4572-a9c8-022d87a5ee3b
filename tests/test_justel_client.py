"""
Tests for Justel client functionality.

Tests rate limiting, robots.txt respect, retry/backoff, and basic functionality.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
import requests
from sources.justel.client import JustelClient


class TestJustelClient:
    """Test suite for JustelClient."""
    
    @pytest.fixture
    def client(self):
        """Create a test client with minimal rate limiting."""
        return JustelClient(rate_limit=10.0)  # 10 req/s for faster tests
    
    @pytest.fixture
    def mock_response(self):
        """Create a mock HTTP response."""
        response = Mock()
        response.status_code = 200
        response.text = "<html><body>Test content</body></html>"
        response.headers = {"content-type": "text/html"}
        return response
    
    def test_client_initialization(self):
        """Test client initialization with default values."""
        client = JustelClient()
        
        assert client.base_url_fr
        assert client.base_url_nl
        assert client.rate_limit > 0
        assert client.session is not None
        assert "ailex-be-ingest" in client.session.headers["User-Agent"]
    
    def test_client_initialization_with_custom_values(self):
        """Test client initialization with custom values."""
        custom_fr = "https://custom.fr"
        custom_nl = "https://custom.nl"
        custom_rate = 2.0
        
        client = JustelClient(
            base_url_fr=custom_fr,
            base_url_nl=custom_nl,
            rate_limit=custom_rate
        )
        
        assert client.base_url_fr == custom_fr
        assert client.base_url_nl == custom_nl
        assert client.rate_limit == custom_rate
    
    def test_rate_limiting(self, client):
        """Test that rate limiting is enforced."""
        # Set a very low rate limit for testing
        client.rate_limit = 2.0  # 2 requests per second
        
        start_time = time.time()
        
        # Make two requests
        client._rate_limit_wait()
        first_request_time = time.time()
        
        client._rate_limit_wait()
        second_request_time = time.time()
        
        # Second request should be delayed by at least 0.5 seconds (1/2.0)
        time_diff = second_request_time - first_request_time
        assert time_diff >= 0.4  # Allow some tolerance
    
    @patch('requests.Session.get')
    def test_robots_txt_respect(self, mock_get, client):
        """Test that robots.txt is respected when configured."""
        # Mock robots.txt response that disallows crawling
        robots_response = Mock()
        robots_response.status_code = 200
        robots_response.text = """
        User-agent: *
        Disallow: /eli/
        """
        
        # Mock document response
        doc_response = Mock()
        doc_response.status_code = 200
        doc_response.text = "<html>Document</html>"
        
        # Configure mock to return robots.txt first, then document
        mock_get.side_effect = [robots_response, doc_response]
        
        client.respect_robots = True
        
        # This should raise an exception due to robots.txt disallow
        with pytest.raises(requests.exceptions.RequestException):
            client._make_request("https://example.com/eli/test")
    
    @patch('requests.Session.get')
    def test_robots_txt_allows_crawling(self, mock_get, client):
        """Test successful request when robots.txt allows crawling."""
        # Mock robots.txt response that allows crawling
        robots_response = Mock()
        robots_response.status_code = 200
        robots_response.text = """
        User-agent: *
        Disallow: /admin/
        """
        
        # Mock document response
        doc_response = Mock()
        doc_response.status_code = 200
        doc_response.text = "<html>Document</html>"
        doc_response.raise_for_status = Mock()
        
        mock_get.side_effect = [robots_response, doc_response]
        
        client.respect_robots = True
        
        # This should succeed
        response = client._make_request("https://example.com/eli/test")
        assert response.text == "<html>Document</html>"
    
    @patch('requests.Session.get')
    def test_retry_on_server_error(self, mock_get, client):
        """Test retry behavior on server errors."""
        # First call returns 500, second call succeeds
        error_response = Mock()
        error_response.status_code = 500
        error_response.raise_for_status.side_effect = requests.exceptions.HTTPError()
        
        success_response = Mock()
        success_response.status_code = 200
        success_response.text = "<html>Success</html>"
        success_response.raise_for_status = Mock()
        
        mock_get.side_effect = [error_response, success_response]
        
        # Should retry and eventually succeed
        response = client._make_request("https://example.com/test")
        assert response.text == "<html>Success</html>"
        assert mock_get.call_count == 2
    
    @patch('requests.Session.get')
    def test_retry_on_rate_limit(self, mock_get, client):
        """Test retry behavior on rate limit (429) errors."""
        # First call returns 429, second call succeeds
        rate_limit_response = Mock()
        rate_limit_response.status_code = 429
        rate_limit_response.raise_for_status.side_effect = requests.exceptions.HTTPError()
        
        success_response = Mock()
        success_response.status_code = 200
        success_response.text = "<html>Success</html>"
        success_response.raise_for_status = Mock()
        
        mock_get.side_effect = [rate_limit_response, success_response]
        
        # Should retry and eventually succeed
        response = client._make_request("https://example.com/test")
        assert response.text == "<html>Success</html>"
        assert mock_get.call_count == 2
    
    @patch('requests.Session.get')
    def test_no_retry_on_client_error(self, mock_get, client):
        """Test that client errors (4xx except 429) are not retried."""
        error_response = Mock()
        error_response.status_code = 404
        error_response.raise_for_status.side_effect = requests.exceptions.HTTPError()
        
        mock_get.return_value = error_response
        
        # Should not retry on 404
        with pytest.raises(requests.exceptions.HTTPError):
            client._make_request("https://example.com/test")
        
        assert mock_get.call_count == 1
    
    @patch('sources.justel.client.JustelClient._make_request')
    def test_get_document_by_numac_success(self, mock_request, client):
        """Test successful document retrieval by NUMAC."""
        mock_response = Mock()
        mock_response.text = "<html><body>Document content</body></html>"
        mock_request.return_value = mock_response
        
        result = client.get_document_by_numac("2022A30600", "fr")
        
        assert result == "<html><body>Document content</body></html>"
        mock_request.assert_called_once()
    
    @patch('sources.justel.client.JustelClient._make_request')
    def test_get_document_by_numac_not_found(self, mock_request, client):
        """Test document retrieval when document is not found."""
        error = requests.exceptions.HTTPError()
        error.response = Mock()
        error.response.status_code = 404
        mock_request.side_effect = error
        
        result = client.get_document_by_numac("INVALID", "fr")
        
        assert result is None
    
    def test_checkpoint_database_initialization(self, client):
        """Test that checkpoint database is properly initialized."""
        import sqlite3
        from common.config import config
        
        # Check that database file exists
        db_path = config.CHECKPOINT_DB
        assert db_path
        
        # Check that tables exist
        with sqlite3.connect(db_path) as conn:
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('visited_urls', 'document_queue', 'processing_log')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            assert 'visited_urls' in tables
            assert 'document_queue' in tables
            assert 'processing_log' in tables
    
    @patch('sources.justel.client.JustelClient._make_request')
    def test_search_documents_placeholder(self, mock_request, client):
        """Test search documents method (placeholder implementation)."""
        # Current implementation is a placeholder that returns empty iterator
        results = list(client.search_documents("test query", "fr"))
        assert results == []
    
    @patch('sources.justel.client.JustelClient._make_request')
    def test_get_recent_documents_placeholder(self, mock_request, client):
        """Test get recent documents method (placeholder implementation)."""
        # Current implementation is a placeholder that returns empty iterator
        results = list(client.get_recent_documents("fr", 30))
        assert results == []
    
    def test_context_manager(self):
        """Test that client works as context manager."""
        with JustelClient() as client:
            assert client.session is not None
        
        # Session should be closed after context exit
        # Note: requests.Session doesn't have a simple way to check if closed
        # so we just verify the context manager works without errors
    
    def test_close_method(self, client):
        """Test explicit close method."""
        session = client.session
        client.close()
        
        # Verify close was called (session object doesn't change, but close() should work)
        # This is mainly to ensure no exceptions are raised
        assert True  # If we get here, close() worked without errors
