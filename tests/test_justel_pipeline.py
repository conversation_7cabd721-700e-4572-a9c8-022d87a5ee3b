"""
Tests for Justel pipeline functionality.

Tests end-to-end HTML → JSON processing, checkpointing, and error recovery.
"""

import pytest
import json
import sqlite3
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from sources.justel.ingest import JustelIngester
from sources.justel.normalize import JustelNormalizer
from common.config import config


class TestJustelPipeline:
    """Test suite for Justel ingestion pipeline."""
    
    @pytest.fixture
    def ingester(self):
        """Create test ingester with mocked dependencies."""
        with patch('sources.justel.ingest.Neo4jClient'), \
             patch('sources.justel.ingest.GlobalRegistry'):
            return JustelIngester()
    
    @pytest.fixture
    def sample_html(self):
        """Sample HTML for testing."""
        return """
        <html>
        <head><title>Test Law 2022A30600</title></head>
        <body>
        <h1>Test Law 2022A30600</h1>
        <div class="numac">NUMAC: 2022A30600</div>
        <div class="publication-date">15 juin 2022</div>
        <div class="eli-identifier">ELI: /eli/loi/2022/06/15/2022A30600</div>
        
        <div class="document-content">
        <div class="article">
        <p><strong>Art. 1.</strong> This is the first article of the test law.</p>
        </div>
        
        <div class="article">
        <p><strong>Art. 2.</strong> This is the second article of the test law.</p>
        </div>
        </div>
        </body>
        </html>
        """
    
    def test_ingester_initialization(self, ingester):
        """Test ingester initialization."""
        assert ingester.client is not None
        assert ingester.normalizer_fr is not None
        assert ingester.normalizer_nl is not None
        assert ingester.neo4j is not None
        assert ingester.registry is not None
    
    def test_directory_creation(self, ingester):
        """Test that required directories are created."""
        # Check that key directories exist
        assert Path(config.OUT_DIR_RAW).exists()
        assert Path(config.OUT_DIR_PROCESSED).exists()
        assert Path(config.LOGS_DIR).exists()
        
        # Check language-specific directories
        assert (Path(config.OUT_DIR_RAW) / "consolidated" / "fr").exists()
        assert (Path(config.OUT_DIR_RAW) / "consolidated" / "nl").exists()
        assert (Path(config.OUT_DIR_PROCESSED) / "fr").exists()
        assert (Path(config.OUT_DIR_PROCESSED) / "nl").exists()
    
    def test_checkpoint_database_initialization(self, ingester):
        """Test checkpoint database initialization."""
        db_path = Path(config.CHECKPOINT_DB)
        assert db_path.exists()
        
        # Check that required tables exist
        with sqlite3.connect(db_path) as conn:
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('visited_urls', 'document_queue', 'processing_log')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            assert 'visited_urls' in tables
            assert 'document_queue' in tables
            assert 'processing_log' in tables
    
    @patch('sources.justel.ingest.JustelIngester._fetch_document_html')
    def test_process_document_success(self, mock_fetch, ingester, sample_html):
        """Test successful document processing."""
        mock_fetch.return_value = sample_html
        
        doc_info = {
            "identifier": "2022A30600",
            "type": "loi",
            "title": "Test Law"
        }
        
        normalizer = JustelNormalizer("fr")
        
        # Mock the storage methods to avoid actual database operations
        with patch.object(ingester, '_store_in_neo4j'), \
             patch.object(ingester, '_store_in_vectors'), \
             patch.object(ingester, '_update_registry'):
            
            result = ingester._process_document(doc_info, normalizer, "fr")
            
            assert result is True
            mock_fetch.assert_called_once_with("2022A30600", "fr")
    
    @patch('sources.justel.ingest.JustelIngester._fetch_document_html')
    def test_process_document_fetch_failure(self, mock_fetch, ingester):
        """Test document processing when fetch fails."""
        mock_fetch.return_value = None  # Simulate fetch failure
        
        doc_info = {
            "identifier": "INVALID",
            "type": "loi",
            "title": "Invalid Document"
        }
        
        normalizer = JustelNormalizer("fr")
        result = ingester._process_document(doc_info, normalizer, "fr")
        
        assert result is False
    
    @patch('sources.justel.ingest.JustelIngester._fetch_document_html')
    def test_process_document_normalization_failure(self, mock_fetch, ingester):
        """Test document processing when normalization fails."""
        # Return invalid HTML that can't be normalized
        mock_fetch.return_value = "<html><body>Invalid content</body></html>"
        
        doc_info = {
            "identifier": "2022A30600",
            "type": "loi",
            "title": "Test Law"
        }
        
        normalizer = JustelNormalizer("fr")
        result = ingester._process_document(doc_info, normalizer, "fr")
        
        assert result is False
    
    def test_save_raw_html(self, ingester, sample_html):
        """Test saving raw HTML to file."""
        identifier = "2022A30600"
        language = "fr"
        
        file_path = ingester._save_raw_html(identifier, language, sample_html)
        
        assert file_path.exists()
        assert file_path.name == f"{identifier}.{language}.html"
        
        # Verify content
        saved_content = file_path.read_text(encoding='utf-8')
        assert saved_content == sample_html
        
        # Cleanup
        file_path.unlink()
    
    def test_save_processed_json(self, ingester, sample_html):
        """Test saving processed JSON to file."""
        # Create test data
        normalizer = JustelNormalizer("fr")
        act, articles = normalizer.normalize_document(sample_html, "2022A30600")
        
        assert act is not None
        assert len(articles) > 0
        
        file_path = ingester._save_processed_json(act, articles, "fr")
        
        assert file_path.exists()
        assert file_path.name == f"{act.id}.json"
        
        # Verify content
        saved_data = json.loads(file_path.read_text(encoding='utf-8'))
        assert "act" in saved_data
        assert "articles" in saved_data
        assert "processed_at" in saved_data
        assert saved_data["act"]["id"] == act.id
        assert len(saved_data["articles"]) == len(articles)
        
        # Cleanup
        file_path.unlink()
    
    def test_ingest_consolidated_law_dry_run(self, ingester):
        """Test dry run mode."""
        result = ingester.ingest_consolidated_law(limit=2, language="fr", dry_run=True)
        
        assert "processed" in result
        assert "successful" in result
        assert "failed" in result
        assert "skipped" in result
        assert result["processed"] > 0
    
    def test_ingest_consolidated_law_french(self, ingester):
        """Test French document ingestion."""
        with patch.object(ingester, '_process_document', return_value=True):
            result = ingester.ingest_consolidated_law(limit=2, language="fr")
            
            assert result["processed"] > 0
            assert result["successful"] > 0
            assert result["failed"] == 0
    
    def test_ingest_consolidated_law_dutch(self, ingester):
        """Test Dutch document ingestion."""
        with patch.object(ingester, '_process_document', return_value=True):
            result = ingester.ingest_consolidated_law(limit=2, language="nl")
            
            assert result["processed"] > 0
            assert result["successful"] > 0
            assert result["failed"] == 0
    
    def test_ingest_consolidated_law_with_failures(self, ingester):
        """Test ingestion with some failures."""
        # Mock process_document to fail on second call
        call_count = 0
        def mock_process(doc_info, normalizer, language):
            nonlocal call_count
            call_count += 1
            return call_count != 2  # Fail on second call
        
        with patch.object(ingester, '_process_document', side_effect=mock_process):
            result = ingester.ingest_consolidated_law(limit=3, language="fr")
            
            assert result["processed"] == 3
            assert result["successful"] == 2
            assert result["failed"] == 1
    
    def test_get_sample_documents_french(self, ingester):
        """Test sample document generation for French."""
        docs = ingester._get_sample_documents("fr", 3)
        
        assert len(docs) == 3
        assert all("identifier" in doc for doc in docs)
        assert all("type" in doc for doc in docs)
        assert all("title" in doc for doc in docs)
    
    def test_get_sample_documents_dutch(self, ingester):
        """Test sample document generation for Dutch."""
        docs = ingester._get_sample_documents("nl", 2)
        
        assert len(docs) == 2
        assert all("identifier" in doc for doc in docs)
        assert all("type" in doc for doc in docs)
        assert all("title" in doc for doc in docs)
    
    def test_get_checkpoint_status_no_checkpoint(self, ingester):
        """Test checkpoint status when no checkpoint exists."""
        # Remove checkpoint file if it exists
        db_path = Path(config.CHECKPOINT_DB)
        if db_path.exists():
            db_path.unlink()
        
        # Reinitialize to create fresh database
        ingester._init_checkpoint_db()
        
        status = ingester.get_checkpoint_status()
        
        assert status["has_checkpoint"] is True  # Database exists but empty
        assert status["total_processed"] == 0
    
    def test_get_checkpoint_status_with_data(self, ingester):
        """Test checkpoint status with existing data."""
        # Add some test data to checkpoint database
        with sqlite3.connect(config.CHECKPOINT_DB) as conn:
            conn.execute("""
                INSERT INTO document_queue (numac, language, status, processed_at)
                VALUES ('2022A30600', 'fr', 'completed', datetime('now'))
            """)
            conn.execute("""
                INSERT INTO document_queue (numac, language, status, processed_at)
                VALUES ('2022A30601', 'fr', 'completed', datetime('now'))
            """)
            conn.commit()
        
        status = ingester.get_checkpoint_status()
        
        assert status["has_checkpoint"] is True
        assert status["total_processed"] == 2
        assert status["last_processed"] is not None
    
    def test_resume_ingestion(self, ingester):
        """Test resume ingestion functionality."""
        with patch.object(ingester, 'ingest_consolidated_law') as mock_ingest:
            mock_ingest.return_value = {"processed": 5, "successful": 5, "failed": 0}
            
            result = ingester.resume_ingestion("fr")
            
            assert result["processed"] == 5
            mock_ingest.assert_called_once_with(language="fr")
    
    def test_end_to_end_html_to_json(self, ingester, sample_html):
        """Test complete end-to-end processing from HTML to JSON."""
        identifier = "2022A30600"
        language = "fr"
        
        # Mock fetch to return our sample HTML
        with patch.object(ingester, '_fetch_document_html', return_value=sample_html), \
             patch.object(ingester, '_store_in_neo4j'), \
             patch.object(ingester, '_store_in_vectors'), \
             patch.object(ingester, '_update_registry'):
            
            doc_info = {
                "identifier": identifier,
                "type": "loi",
                "title": "Test Law"
            }
            
            normalizer = JustelNormalizer(language)
            result = ingester._process_document(doc_info, normalizer, language)
            
            assert result is True
            
            # Check that raw HTML was saved
            raw_path = Path(config.OUT_DIR_RAW) / "consolidated" / language / f"{identifier}.{language}.html"
            assert raw_path.exists()
            
            # Check that processed JSON was saved
            processed_files = list(Path(config.OUT_DIR_PROCESSED).glob(f"**/{identifier}.json"))
            assert len(processed_files) > 0
            
            # Verify JSON content
            json_path = processed_files[0]
            data = json.loads(json_path.read_text(encoding='utf-8'))
            assert "act" in data
            assert "articles" in data
            assert data["act"]["id"] == identifier
            assert len(data["articles"]) >= 1
            
            # Cleanup
            raw_path.unlink()
            json_path.unlink()
    
    def test_error_recovery(self, ingester):
        """Test error recovery and continuation."""
        # Simulate processing with errors
        call_count = 0
        def mock_process_with_errors(doc_info, normalizer, language):
            nonlocal call_count
            call_count += 1
            if call_count == 2:
                raise Exception("Simulated processing error")
            return True
        
        with patch.object(ingester, '_process_document', side_effect=mock_process_with_errors):
            result = ingester.ingest_consolidated_law(limit=3, language="fr")
            
            # Should continue processing despite error
            assert result["processed"] == 3
            assert result["successful"] == 2
            assert result["failed"] == 1
            assert len(result["errors"]) == 1
    
    def test_close_method(self, ingester):
        """Test ingester close method."""
        # Mock the close methods to avoid actual cleanup
        ingester.client.close = Mock()
        ingester.neo4j.close = Mock()
        ingester.registry.close = Mock()
        
        ingester.close()
        
        ingester.client.close.assert_called_once()
        ingester.neo4j.close.assert_called_once()
        ingester.registry.close.assert_called_once()
