"""
Tests for Justel parser functionality.

Tests FR/NL article splitting, heading extraction, hyphen merge, TOC extraction.
"""

import pytest
from datetime import date
from sources.justel.parse import JustelParser


class TestJustelParser:
    """Test suite for JustelParser."""
    
    @pytest.fixture
    def parser_fr(self):
        """Create French parser."""
        return JustelParser("fr")
    
    @pytest.fixture
    def parser_nl(self):
        """Create Dutch parser."""
        return JustelParser("nl")
    
    @pytest.fixture
    def sample_html_fr(self):
        """Sample French legal document HTML."""
        return """
        <html>
        <head><title>Loi du 25 mai 2018 relative à la protection des données</title></head>
        <body>
        <h1>Loi du 25 mai 2018 relative à la protection des données</h1>
        <div class="numac">NUMAC: 2018A30600</div>
        <div class="publication-date">25 mai 2018</div>
        <div class="eli-identifier">ELI: /eli/loi/2018/05/25/2018A30600</div>
        
        <div class="table-of-contents">
        <h2>Table des matières</h2>
        <ul>
        <li>Art. 1 - Définitions</li>
        <li>Art. 2 - Champ d'application</li>
        <li>Art. 3 - Principes généraux</li>
        </ul>
        </div>
        
        <div class="document-content">
        <h2>CHAPITRE I - Dispositions générales</h2>
        
        <div class="article">
        <p><strong>Art. 1.</strong> Pour l'application de la présente loi, on entend par :</p>
        <p>1° "données à caractère personnel" : toute information se rapportant à une personne physique identifiée ou identifiable;</p>
        <p>2° "traitement" : toute opération ou tout ensemble d'opérations effectuées ou non à l'aide de procédés automatisés.</p>
        </div>
        
        <div class="article">
        <p><strong>Art. 2.</strong> La présente loi s'applique au traitement de données à caractère personnel, automatisé en tout ou en partie.</p>
        </div>
        
        <h2>CHAPITRE II - Principes</h2>
        
        <div class="article">
        <p><strong>Art. 3.</strong> Le traitement de données à caractère personnel doit être :</p>
        <p>a) licite, loyal et transparent;</p>
        <p>b) collecté pour des finalités déterminées, explicites et légitimes.</p>
        </div>
        </div>
        </body>
        </html>
        """
    
    @pytest.fixture
    def sample_html_nl(self):
        """Sample Dutch legal document HTML."""
        return """
        <html>
        <head><title>Wet van 25 mei 2018 betreffende gegevensbescherming</title></head>
        <body>
        <h1>Wet van 25 mei 2018 betreffende gegevensbescherming</h1>
        <div class="numac">NUMAC: 2018A30600</div>
        <div class="datum-publicatie">25 mei 2018</div>
        <div class="eli-identifier">ELI: /eli/wet/2018/05/25/2018A30600</div>
        
        <div class="inhoudsopgave">
        <h2>Inhoudsopgave</h2>
        <ul>
        <li>Art. 1 - Definities</li>
        <li>Art. 2 - Toepassingsgebied</li>
        <li>Art. 3 - Algemene beginselen</li>
        </ul>
        </div>
        
        <div class="document-content">
        <h2>HOOFDSTUK I - Algemene bepalingen</h2>
        
        <div class="artikel">
        <p><strong>Art. 1.</strong> Voor de toepassing van deze wet wordt verstaan onder:</p>
        <p>1° "persoonsgegevens": alle informatie betreffende een geïdentificeerde of identificeerbare natuurlijke persoon;</p>
        <p>2° "verwerking": elke bewerking of elk geheel van bewerkingen met betrekking tot persoonsgegevens.</p>
        </div>
        
        <div class="artikel">
        <p><strong>Art. 2.</strong> Deze wet is van toepassing op de verwerking van persoonsgegevens, geheel of gedeeltelijk geautomatiseerd.</p>
        </div>
        
        <h2>HOOFDSTUK II - Beginselen</h2>
        
        <div class="artikel">
        <p><strong>Art. 3.</strong> De verwerking van persoonsgegevens moet:</p>
        <p>a) rechtmatig, behoorlijk en transparant zijn;</p>
        <p>b) verzameld worden voor welbepaalde, uitdrukkelijke en gerechtvaardigde doeleinden.</p>
        </div>
        </div>
        </body>
        </html>
        """
    
    def test_parser_initialization_french(self, parser_fr):
        """Test French parser initialization."""
        assert parser_fr.language == "fr"
        assert parser_fr.selectors is not None
        assert parser_fr.patterns is not None
        assert parser_fr.markers is not None
    
    def test_parser_initialization_dutch(self, parser_nl):
        """Test Dutch parser initialization."""
        assert parser_nl.language == "nl"
        assert parser_nl.selectors is not None
        assert parser_nl.patterns is not None
        assert parser_nl.markers is not None
    
    def test_parse_document_french(self, parser_fr, sample_html_fr):
        """Test parsing complete French document."""
        result = parser_fr.parse_document(sample_html_fr)
        
        assert result is not None
        assert result["language"] == "fr"
        assert "metadata" in result
        assert "structure" in result
        assert "articles" in result
        assert "references" in result
    
    def test_parse_document_dutch(self, parser_nl, sample_html_nl):
        """Test parsing complete Dutch document."""
        result = parser_nl.parse_document(sample_html_nl)
        
        assert result is not None
        assert result["language"] == "nl"
        assert "metadata" in result
        assert "structure" in result
        assert "articles" in result
        assert "references" in result
    
    def test_extract_metadata_french(self, parser_fr, sample_html_fr):
        """Test metadata extraction from French document."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_fr, 'html.parser')
        
        metadata = parser_fr._extract_metadata(soup)
        
        assert metadata is not None
        assert "title" in metadata
        assert "numac" in metadata
        assert "eli" in metadata
        assert "publication_date" in metadata
        assert metadata["numac"] == "2018A30600"
        assert metadata["eli"] == "/eli/loi/2018/05/25/2018A30600"
    
    def test_extract_metadata_dutch(self, parser_nl, sample_html_nl):
        """Test metadata extraction from Dutch document."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_nl, 'html.parser')
        
        metadata = parser_nl._extract_metadata(soup)
        
        assert metadata is not None
        assert "title" in metadata
        assert "numac" in metadata
        assert "eli" in metadata
        assert metadata["numac"] == "2018A30600"
        assert metadata["eli"] == "/eli/wet/2018/05/25/2018A30600"
    
    def test_extract_numac(self, parser_fr, sample_html_fr):
        """Test NUMAC extraction."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_fr, 'html.parser')
        
        numac = parser_fr._extract_numac(soup)
        assert numac == "2018A30600"
    
    def test_extract_eli(self, parser_fr, sample_html_fr):
        """Test ELI extraction."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_fr, 'html.parser')
        
        eli = parser_fr._extract_eli(soup)
        assert eli == "/eli/loi/2018/05/25/2018A30600"
    
    def test_extract_publication_date_french(self, parser_fr):
        """Test publication date extraction from French text."""
        test_html = '<div class="publication-date">25 mai 2018</div>'
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(test_html, 'html.parser')
        
        pub_date = parser_fr._extract_publication_date(soup)
        assert pub_date == date(2018, 5, 25)
    
    def test_extract_publication_date_dutch(self, parser_nl):
        """Test publication date extraction from Dutch text."""
        test_html = '<div class="datum-publicatie">25 mei 2018</div>'
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(test_html, 'html.parser')
        
        pub_date = parser_nl._extract_publication_date(soup)
        assert pub_date == date(2018, 5, 25)
    
    def test_extract_document_type_french(self, parser_fr):
        """Test document type extraction from French title."""
        doc_type = parser_fr._extract_document_type(None, "Loi du 25 mai 2018 relative à la protection")
        assert doc_type == "loi"
        
        doc_type = parser_fr._extract_document_type(None, "Arrêté royal du 15 juin 2020")
        assert doc_type == "arrete_royal"
        
        doc_type = parser_fr._extract_document_type(None, "Décret du 10 mars 2021")
        assert doc_type == "decret"
    
    def test_extract_document_type_dutch(self, parser_nl):
        """Test document type extraction from Dutch title."""
        doc_type = parser_nl._extract_document_type(None, "Wet van 25 mei 2018 betreffende gegevensbescherming")
        assert doc_type == "wet"
        
        doc_type = parser_nl._extract_document_type(None, "Koninklijk besluit van 15 juni 2020")
        assert doc_type == "koninklijk_besluit"
        
        doc_type = parser_nl._extract_document_type(None, "Decreet van 10 maart 2021")
        assert doc_type == "decreet"
    
    def test_extract_structure_french(self, parser_fr, sample_html_fr):
        """Test structure extraction from French document."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_fr, 'html.parser')
        
        structure = parser_fr._extract_structure(soup)
        
        assert "chapters" in structure
        assert "sections" in structure
        assert "toc" in structure
        assert len(structure["chapters"]) >= 1  # Should find at least one chapter
    
    def test_extract_articles_french(self, parser_fr, sample_html_fr):
        """Test article extraction from French document."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_fr, 'html.parser')
        
        articles = parser_fr._extract_articles(soup)
        
        assert len(articles) == 3  # Should find 3 articles
        assert articles[0]["number"] == "1"
        assert articles[1]["number"] == "2"
        assert articles[2]["number"] == "3"
        assert all("text" in article for article in articles)
    
    def test_extract_articles_dutch(self, parser_nl, sample_html_nl):
        """Test article extraction from Dutch document."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_nl, 'html.parser')
        
        articles = parser_nl._extract_articles(soup)
        
        assert len(articles) == 3  # Should find 3 articles
        assert articles[0]["number"] == "1"
        assert articles[1]["number"] == "2"
        assert articles[2]["number"] == "3"
        assert all("text" in article for article in articles)
    
    def test_extract_references(self, parser_fr, sample_html_fr):
        """Test legal reference extraction."""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(sample_html_fr, 'html.parser')
        
        references = parser_fr._extract_references(soup)
        
        # Should find NUMAC and ELI references
        numac_refs = [ref for ref in references if ref["type"] == "numac"]
        eli_refs = [ref for ref in references if ref["type"] == "eli"]
        
        assert len(numac_refs) >= 1
        assert len(eli_refs) >= 1
        assert numac_refs[0]["identifier"] == "2018A30600"
    
    def test_parse_date_formats(self, parser_fr):
        """Test various date format parsing."""
        # Full date format
        date_result = parser_fr._parse_date("25 mai 2018")
        assert date_result == date(2018, 5, 25)
        
        # Short date format
        date_result = parser_fr._parse_date("25/05/2018")
        assert date_result == date(2018, 5, 25)
        
        # Invalid date
        date_result = parser_fr._parse_date("invalid date")
        assert date_result is None
    
    def test_clean_text(self, parser_fr):
        """Test text cleaning functionality."""
        dirty_text = "  This   is    messy   text.  With   extra   spaces.  "
        clean_text = parser_fr._clean_text(dirty_text)
        
        assert clean_text == "This is messy text. With extra spaces."
        
        # Test empty text
        assert parser_fr._clean_text("") == ""
        assert parser_fr._clean_text(None) == ""
    
    def test_article_pattern_matching_french(self, parser_fr):
        """Test French article pattern matching."""
        import re
        
        # Test various article formats
        test_cases = [
            ("Art. 1.", "1"),
            ("Article 2.", "2"),
            ("Art. 3bis.", "3bis"),
            ("Art. 4.1.", "4.1"),
            ("Art. 5a.", "5a")
        ]
        
        for text, expected in test_cases:
            match = re.search(parser_fr.patterns["article_pattern"], text)
            assert match is not None
            assert match.group(1) == expected
    
    def test_article_pattern_matching_dutch(self, parser_nl):
        """Test Dutch article pattern matching."""
        import re
        
        # Test various article formats
        test_cases = [
            ("Art. 1.", "1"),
            ("Artikel 2.", "2"),
            ("Art. 3bis.", "3bis"),
            ("Art. 4.1.", "4.1"),
            ("Art. 5a.", "5a")
        ]
        
        for text, expected in test_cases:
            match = re.search(parser_nl.patterns["article_pattern"], text)
            assert match is not None
            assert match.group(1) == expected
    
    def test_chapter_pattern_matching_french(self, parser_fr):
        """Test French chapter pattern matching."""
        import re
        
        test_cases = [
            ("CHAPITRE I", "I"),
            ("CHAPITRE II - Dispositions générales", "II"),
            ("CHAPITRE 1", "1"),
            ("CHAPITRE 2 - Principes", "2")
        ]
        
        for text, expected in test_cases:
            match = re.search(parser_fr.patterns["heading_pattern"], text)
            assert match is not None
            assert match.group(1) == expected
    
    def test_chapter_pattern_matching_dutch(self, parser_nl):
        """Test Dutch chapter pattern matching."""
        import re
        
        test_cases = [
            ("HOOFDSTUK I", "I"),
            ("HOOFDSTUK II - Algemene bepalingen", "II"),
            ("HOOFDSTUK 1", "1"),
            ("HOOFDSTUK 2 - Beginselen", "2")
        ]
        
        for text, expected in test_cases:
            match = re.search(parser_nl.patterns["heading_pattern"], text)
            assert match is not None
            assert match.group(1) == expected
