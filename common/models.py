"""
Unified data models for ailex-be-ingest.

These models provide a common interface for all legal documents regardless of source
(Vlaamse Codex, EUR-Lex, CELLAR). This enables transparent querying by downstream agents.
"""

from datetime import date as Date
from typing import Optional, List, Literal, Dict
from pydantic import BaseModel, Field


class CommonAct(BaseModel):
    """
    Unified model for legal acts from any source.
    
    This model abstracts away source-specific differences and provides
    a consistent interface for all legal documents.
    """
    id: str = Field(..., description="Unique identifier: CELEX for EU, codexId for Vlaamse")
    title: str = Field(..., description="Full title of the legal act")
    date: Date = Field(..., description="Publication or adoption date")
    language: str = Field(..., description="Language code (nl, fr, de, en)")
    source: Literal["vlaamse", "eu_dump", "eu_rest", "justel_consolidated"] = Field(..., description="Source system")
    eli: Optional[str] = Field(None, description="European Legislation Identifier")
    
    # Source-specific metadata (stored as JSON in Neo4j)
    metadata: dict = Field(default_factory=dict, description="Source-specific metadata")
    
    class Config:
        json_encoders = {
            Date: lambda v: v.isoformat()
        }


class CommonArticle(BaseModel):
    """
    Unified model for articles within legal acts.
    
    Articles are the atomic units of legal text that get embedded
    and stored in the vector database.
    """
    id: str = Field(..., description="Unique identifier: actId + article number")
    act_id: str = Field(..., description="ID of the parent act")
    number: str = Field(..., description="Article number or identifier")
    heading: Optional[str] = Field(None, description="Article heading/title")
    text: str = Field(..., description="Full text content of the article")
    language: str = Field(..., description="Language code (nl, fr, de, en)")
    
    # Additional metadata for better retrieval
    metadata: dict = Field(default_factory=dict, description="Additional article metadata")


class LegalRelationship(BaseModel):
    """
    Model for relationships between legal acts.
    
    Used to create the knowledge graph connections in Neo4j.
    """
    source_act_id: str = Field(..., description="Source act ID")
    target_act_id: str = Field(..., description="Target act ID")
    relationship_type: Literal["AMENDS", "REPEALS", "CITES", "IMPLEMENTS"] = Field(
        ..., description="Type of legal relationship"
    )
    article_reference: Optional[str] = Field(None, description="Specific article reference if applicable")
    metadata: dict = Field(default_factory=dict, description="Additional relationship metadata")


# Neo4j node labels and relationship types
NEO4J_LABELS = {
    "ACT": "Act",
    "ARTICLE": "Article"
}

NEO4J_RELATIONSHIPS = {
    "HAS_ARTICLE": "HAS_ARTICLE",
    "AMENDS": "AMENDS", 
    "REPEALS": "REPEALS",
    "CITES": "CITES",
    "IMPLEMENTS": "IMPLEMENTS"
}

# Pinecone namespace mapping
PINECONE_NAMESPACES = {
    "vlaamse": "vlaamse_codex",
    "eu_dump": "eu_legislation",
    "eu_rest": "eu_legislation",
    "justel_consolidated": "belgian_law"
}


def create_vector_id(source: str, act_id: str, article_id: str, chunk_index: int = 0) -> str:
    """
    Create deterministic vector ID for idempotent upserts.

    Format: {source}:{act_id}:{article_id}:{chunk_index}

    Args:
        source: Source system (vlaamse, eu_dump, eu_rest, justel_consolidated)
        act_id: Act identifier
        article_id: Article identifier
        chunk_index: Chunk index within article

    Returns:
        Deterministic vector ID string
    """
    return f"{source}:{act_id}:{article_id}:{chunk_index}"


def parse_vector_id(vector_id: str) -> Dict[str, str]:
    """
    Parse vector ID back into components.

    Args:
        vector_id: Vector ID string

    Returns:
        Dictionary with source, act_id, article_id, chunk_index
    """
    parts = vector_id.split(':')
    if len(parts) != 4:
        raise ValueError(f"Invalid vector ID format: {vector_id}")

    return {
        'source': parts[0],
        'act_id': parts[1],
        'article_id': parts[2],
        'chunk_index': int(parts[3])
    }


# Belgian legal document patterns
NUMAC_PATTERN = r"^\d{4}[A-Z]\d{5}$"  # e.g., 2022A30600
ELI_PATTERN_BE = r"^/eli/(loi|wet|arrete|besluit)/\d{4}/\d{2}/\d{2}/\w+$"  # Allow alphanumeric at end
