#!/bin/bash

# Fetch test fixtures for Justel integration
# This script downloads sample legal documents for testing

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FIXTURES_DIR="$PROJECT_ROOT/tests/fixtures"
HTML_DIR="$FIXTURES_DIR/html"
GOLDEN_DIR="$FIXTURES_DIR/golden"

echo "🔍 Fetching Justel test fixtures..."

# Create directories
mkdir -p "$HTML_DIR"
mkdir -p "$GOLDEN_DIR"

# Sample NUMAC for testing
SAMPLE_NUMAC="2022A30600"

echo "📁 Created fixture directories:"
echo "  - $HTML_DIR"
echo "  - $GOLDEN_DIR"

# Function to create sample HTML fixture
create_sample_html_fr() {
    local file_path="$HTML_DIR/${SAMPLE_NUMAC}.fr.html"
    
    cat > "$file_path" << 'EOF'
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Loi du 15 juin 2022 relative à la protection des données personnelles</title>
</head>
<body>
    <div class="document-header">
        <h1>Loi du 15 juin 2022 relative à la protection des données personnelles</h1>
        <div class="metadata">
            <div class="numac">NUMAC: 2022A30600</div>
            <div class="publication-date">15 juin 2022</div>
            <div class="eli-identifier">ELI: /eli/loi/2022/06/15/2022A30600</div>
            <div class="document-type">Loi</div>
        </div>
    </div>
    
    <div class="table-of-contents">
        <h2>Table des matières</h2>
        <ul>
            <li><a href="#art1">Art. 1 - Objet et champ d'application</a></li>
            <li><a href="#art2">Art. 2 - Définitions</a></li>
            <li><a href="#art3">Art. 3 - Principes généraux</a></li>
            <li><a href="#art4">Art. 4 - Droits des personnes concernées</a></li>
            <li><a href="#art5">Art. 5 - Obligations du responsable du traitement</a></li>
            <li><a href="#art6">Art. 6 - Mesures de sécurité</a></li>
            <li><a href="#art7">Art. 7 - Sanctions</a></li>
            <li><a href="#art8">Art. 8 - Dispositions transitoires</a></li>
        </ul>
    </div>
    
    <div class="document-content">
        <h2>CHAPITRE I - Dispositions générales</h2>
        
        <div class="article" id="art1">
            <h3>Article 1er - Objet et champ d'application</h3>
            <p><strong>Art. 1.</strong> La présente loi a pour objet de protéger les personnes physiques à l'égard du traitement des données à caractère personnel et de garantir la libre circulation de ces données.</p>
            <p>Elle s'applique au traitement de données à caractère personnel, automatisé en tout ou en partie, ainsi qu'au traitement non automatisé de données à caractère personnel contenues ou appelées à figurer dans un fichier.</p>
        </div>
        
        <div class="article" id="art2">
            <h3>Article 2 - Définitions</h3>
            <p><strong>Art. 2.</strong> Pour l'application de la présente loi, on entend par :</p>
            <p>1° "données à caractère personnel" : toute information se rapportant à une personne physique identifiée ou identifiable (ci-après dénommée "personne concernée") ;</p>
            <p>2° "traitement" : toute opération ou tout ensemble d'opérations effectuées ou non à l'aide de procédés automatisés et appliquées à des données ou des ensembles de données à caractère personnel ;</p>
            <p>3° "responsable du traitement" : la personne physique ou morale, l'autorité publique, le service ou un autre organisme qui, seul ou conjointement avec d'autres, détermine les finalités et les moyens du traitement ;</p>
            <p>4° "sous-traitant" : la personne physique ou morale, l'autorité publique, le service ou un autre organisme qui traite des données à caractère personnel pour le compte du responsable du traitement.</p>
        </div>
        
        <h2>CHAPITRE II - Principes</h2>
        
        <div class="article" id="art3">
            <h3>Article 3 - Principes généraux</h3>
            <p><strong>Art. 3.</strong> Le traitement de données à caractère personnel doit respecter les principes suivants :</p>
            <p>a) licéité, loyauté et transparence : les données sont traitées de manière licite, loyale et transparente au regard de la personne concernée ;</p>
            <p>b) limitation des finalités : les données sont collectées pour des finalités déterminées, explicites et légitimes ;</p>
            <p>c) minimisation des données : les données sont adéquates, pertinentes et limitées à ce qui est nécessaire ;</p>
            <p>d) exactitude : les données sont exactes et, si nécessaire, tenues à jour ;</p>
            <p>e) limitation de la conservation : les données sont conservées sous une forme permettant l'identification des personnes concernées pendant une durée n'excédant pas celle nécessaire ;</p>
            <p>f) intégrité et confidentialité : les données sont traitées de façon à garantir une sécurité appropriée.</p>
        </div>
        
        <div class="article" id="art4">
            <h3>Article 4 - Droits des personnes concernées</h3>
            <p><strong>Art. 4.</strong> Toute personne concernée a le droit :</p>
            <p>1° d'être informée du traitement de ses données à caractère personnel ;</p>
            <p>2° d'accéder à ses données à caractère personnel ;</p>
            <p>3° de rectifier ses données à caractère personnel ;</p>
            <p>4° d'effacer ses données à caractère personnel ;</p>
            <p>5° de limiter le traitement de ses données à caractère personnel ;</p>
            <p>6° à la portabilité de ses données à caractère personnel ;</p>
            <p>7° de s'opposer au traitement de ses données à caractère personnel.</p>
        </div>
        
        <h2>CHAPITRE III - Obligations</h2>
        
        <div class="article" id="art5">
            <h3>Article 5 - Obligations du responsable du traitement</h3>
            <p><strong>Art. 5.</strong> Le responsable du traitement met en œuvre les mesures techniques et organisationnelles appropriées pour assurer et être en mesure de démontrer que le traitement est effectué conformément à la présente loi.</p>
            <p>Ces mesures sont réexaminées et actualisées si nécessaire.</p>
        </div>
        
        <div class="article" id="art6">
            <h3>Article 6 - Mesures de sécurité</h3>
            <p><strong>Art. 6.</strong> Le responsable du traitement et le sous-traitant mettent en œuvre les mesures techniques et organisationnelles appropriées afin de garantir un niveau de sécurité adapté au risque.</p>
            <p>Ces mesures comprennent notamment la pseudonymisation et le chiffrement des données à caractère personnel.</p>
        </div>
        
        <h2>CHAPITRE IV - Sanctions et dispositions finales</h2>
        
        <div class="article" id="art7">
            <h3>Article 7 - Sanctions</h3>
            <p><strong>Art. 7.</strong> Les infractions aux dispositions de la présente loi sont punies conformément aux dispositions du Code pénal et aux règlements d'exécution.</p>
        </div>
        
        <div class="article" id="art8">
            <h3>Article 8 - Dispositions transitoires</h3>
            <p><strong>Art. 8.</strong> La présente loi entre en vigueur le jour de sa publication au Moniteur belge.</p>
            <p>Les traitements en cours à la date d'entrée en vigueur de la présente loi doivent être mis en conformité dans un délai de six mois.</p>
        </div>
    </div>
    
    <div class="document-footer">
        <p>Publié au Moniteur belge le 15 juin 2022</p>
        <p>Source: Service Public Fédéral Justice - Base de données Justel</p>
    </div>
</body>
</html>
EOF

    echo "✅ Created French HTML fixture: $file_path"
}

# Function to create sample Dutch HTML fixture
create_sample_html_nl() {
    local file_path="$HTML_DIR/${SAMPLE_NUMAC}.nl.html"
    
    cat > "$file_path" << 'EOF'
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>Wet van 15 juni 2022 betreffende de bescherming van persoonsgegevens</title>
</head>
<body>
    <div class="document-header">
        <h1>Wet van 15 juni 2022 betreffende de bescherming van persoonsgegevens</h1>
        <div class="metadata">
            <div class="numac">NUMAC: 2022A30600</div>
            <div class="datum-publicatie">15 juni 2022</div>
            <div class="eli-identifier">ELI: /eli/wet/2022/06/15/2022A30600</div>
            <div class="document-type">Wet</div>
        </div>
    </div>
    
    <div class="inhoudsopgave">
        <h2>Inhoudsopgave</h2>
        <ul>
            <li><a href="#art1">Art. 1 - Voorwerp en toepassingsgebied</a></li>
            <li><a href="#art2">Art. 2 - Definities</a></li>
            <li><a href="#art3">Art. 3 - Algemene beginselen</a></li>
            <li><a href="#art4">Art. 4 - Rechten van betrokkenen</a></li>
            <li><a href="#art5">Art. 5 - Verplichtingen van de verwerkingsverantwoordelijke</a></li>
            <li><a href="#art6">Art. 6 - Beveiligingsmaatregelen</a></li>
            <li><a href="#art7">Art. 7 - Sancties</a></li>
            <li><a href="#art8">Art. 8 - Overgangsbepalingen</a></li>
        </ul>
    </div>
    
    <div class="document-content">
        <h2>HOOFDSTUK I - Algemene bepalingen</h2>
        
        <div class="artikel" id="art1">
            <h3>Artikel 1 - Voorwerp en toepassingsgebied</h3>
            <p><strong>Art. 1.</strong> Deze wet heeft tot doel natuurlijke personen te beschermen bij de verwerking van persoonsgegevens en het vrije verkeer van deze gegevens te waarborgen.</p>
            <p>Zij is van toepassing op de verwerking van persoonsgegevens, geheel of gedeeltelijk geautomatiseerd, alsook op de niet-geautomatiseerde verwerking van persoonsgegevens die zijn opgenomen of bestemd zijn om te worden opgenomen in een bestand.</p>
        </div>
        
        <div class="artikel" id="art2">
            <h3>Artikel 2 - Definities</h3>
            <p><strong>Art. 2.</strong> Voor de toepassing van deze wet wordt verstaan onder:</p>
            <p>1° "persoonsgegevens": alle informatie betreffende een geïdentificeerde of identificeerbare natuurlijke persoon (hierna "betrokkene" genoemd);</p>
            <p>2° "verwerking": elke bewerking of elk geheel van bewerkingen met betrekking tot persoonsgegevens of een geheel van persoonsgegevens, al dan niet uitgevoerd via geautomatiseerde procédés;</p>
            <p>3° "verwerkingsverantwoordelijke": de natuurlijke of rechtspersoon, de overheidsinstantie, de dienst of enig ander orgaan die/dat, alleen of samen met anderen, het doel van en de middelen voor de verwerking van persoonsgegevens vaststelt;</p>
            <p>4° "verwerker": de natuurlijke of rechtspersoon, de overheidsinstantie, de dienst of enig ander orgaan die/dat ten behoeve van de verwerkingsverantwoordelijke persoonsgegevens verwerkt.</p>
        </div>
        
        <h2>HOOFDSTUK II - Beginselen</h2>
        
        <div class="artikel" id="art3">
            <h3>Artikel 3 - Algemene beginselen</h3>
            <p><strong>Art. 3.</strong> De verwerking van persoonsgegevens moet de volgende beginselen respecteren:</p>
            <p>a) rechtmatigheid, behoorlijkheid en transparantie: de gegevens worden op rechtmatige, behoorlijke en transparante wijze verwerkt ten aanzien van de betrokkene;</p>
            <p>b) doelbinding: de gegevens worden verzameld voor welbepaalde, uitdrukkelijke en gerechtvaardigde doeleinden;</p>
            <p>c) gegevensminimalisatie: de gegevens zijn toereikend, ter zake dienend en beperkt tot wat noodzakelijk is;</p>
            <p>d) juistheid: de gegevens zijn juist en zo nodig bijgewerkt;</p>
            <p>e) opslagbeperking: de gegevens worden bewaard in een vorm die het mogelijk maakt de betrokkenen te identificeren gedurende een periode die niet langer is dan noodzakelijk;</p>
            <p>f) integriteit en vertrouwelijkheid: de gegevens worden verwerkt op een wijze die een passende beveiliging waarborgt.</p>
        </div>
        
        <div class="artikel" id="art4">
            <h3>Artikel 4 - Rechten van betrokkenen</h3>
            <p><strong>Art. 4.</strong> Elke betrokkene heeft het recht:</p>
            <p>1° om geïnformeerd te worden over de verwerking van zijn persoonsgegevens;</p>
            <p>2° van toegang tot zijn persoonsgegevens;</p>
            <p>3° op rectificatie van zijn persoonsgegevens;</p>
            <p>4° op wissing van zijn persoonsgegevens;</p>
            <p>5° op beperking van de verwerking van zijn persoonsgegevens;</p>
            <p>6° op overdraagbaarheid van zijn persoonsgegevens;</p>
            <p>7° van bezwaar tegen de verwerking van zijn persoonsgegevens.</p>
        </div>
        
        <h2>HOOFDSTUK III - Verplichtingen</h2>
        
        <div class="artikel" id="art5">
            <h3>Artikel 5 - Verplichtingen van de verwerkingsverantwoordelijke</h3>
            <p><strong>Art. 5.</strong> De verwerkingsverantwoordelijke implementeert passende technische en organisatorische maatregelen om te waarborgen en aan te kunnen tonen dat de verwerking in overeenstemming met deze wet wordt uitgevoerd.</p>
            <p>Deze maatregelen worden indien nodig herzien en bijgewerkt.</p>
        </div>
        
        <div class="artikel" id="art6">
            <h3>Artikel 6 - Beveiligingsmaatregelen</h3>
            <p><strong>Art. 6.</strong> De verwerkingsverantwoordelijke en de verwerker implementeren passende technische en organisatorische maatregelen om een op het risico afgestemd beveiligingsniveau te waarborgen.</p>
            <p>Deze maatregelen omvatten onder meer de pseudonimisering en versleuteling van persoonsgegevens.</p>
        </div>
        
        <h2>HOOFDSTUK IV - Sancties en slotbepalingen</h2>
        
        <div class="artikel" id="art7">
            <h3>Artikel 7 - Sancties</h3>
            <p><strong>Art. 7.</strong> Inbreuken op de bepalingen van deze wet worden bestraft overeenkomstig de bepalingen van het Strafwetboek en de uitvoeringsbesluiten.</p>
        </div>
        
        <div class="artikel" id="art8">
            <h3>Artikel 8 - Overgangsbepalingen</h3>
            <p><strong>Art. 8.</strong> Deze wet treedt in werking op de dag van haar bekendmaking in het Belgisch Staatsblad.</p>
            <p>Verwerkingen die lopende zijn op de datum van inwerkingtreding van deze wet moeten binnen zes maanden in overeenstemming worden gebracht.</p>
        </div>
    </div>
    
    <div class="document-footer">
        <p>Bekendgemaakt in het Belgisch Staatsblad op 15 juni 2022</p>
        <p>Bron: Federale Overheidsdienst Justitie - Databank Justel</p>
    </div>
</body>
</html>
EOF

    echo "✅ Created Dutch HTML fixture: $file_path"
}

# Function to create sample golden data
create_golden_data() {
    local lang="$1"
    local file_path="$GOLDEN_DIR/${SAMPLE_NUMAC}.${lang}.json"
    
    if [ "$lang" = "fr" ]; then
        cat > "$file_path" << 'EOF'
{
  "act": {
    "id": "2022A30600",
    "title": "Loi du 15 juin 2022 relative à la protection des données personnelles",
    "numac": "2022A30600",
    "eli": "/eli/loi/2022/06/15/2022A30600",
    "document_type": "loi",
    "publication_date": "2022-06-15"
  },
  "articles": [
    {
      "number": "1",
      "content_hash": -**********,
      "text_preview": "La présente loi a pour objet de protéger les personnes physiques..."
    },
    {
      "number": "2", 
      "content_hash": -**********,
      "text_preview": "Pour l'application de la présente loi, on entend par..."
    },
    {
      "number": "3",
      "content_hash": -**********,
      "text_preview": "Le traitement de données à caractère personnel doit respecter..."
    },
    {
      "number": "4",
      "content_hash": -1234567893,
      "text_preview": "Toute personne concernée a le droit..."
    },
    {
      "number": "5",
      "content_hash": -1234567894,
      "text_preview": "Le responsable du traitement met en œuvre..."
    },
    {
      "number": "6",
      "content_hash": -1234567895,
      "text_preview": "Le responsable du traitement et le sous-traitant..."
    },
    {
      "number": "7",
      "content_hash": -1234567896,
      "text_preview": "Les infractions aux dispositions de la présente loi..."
    },
    {
      "number": "8",
      "content_hash": -1234567897,
      "text_preview": "La présente loi entre en vigueur..."
    }
  ],
  "metadata": {
    "total_articles": 8,
    "chapters": 4,
    "language": "fr",
    "fixture_version": "1.0",
    "created_at": "2025-01-28T12:00:00Z"
  }
}
EOF
    else
        cat > "$file_path" << 'EOF'
{
  "act": {
    "id": "2022A30600",
    "title": "Wet van 15 juni 2022 betreffende de bescherming van persoonsgegevens",
    "numac": "2022A30600",
    "eli": "/eli/wet/2022/06/15/2022A30600",
    "document_type": "wet",
    "publication_date": "2022-06-15"
  },
  "articles": [
    {
      "number": "1",
      "content_hash": -2234567890,
      "text_preview": "Deze wet heeft tot doel natuurlijke personen te beschermen..."
    },
    {
      "number": "2",
      "content_hash": -2234567891,
      "text_preview": "Voor de toepassing van deze wet wordt verstaan onder..."
    },
    {
      "number": "3",
      "content_hash": -2234567892,
      "text_preview": "De verwerking van persoonsgegevens moet de volgende beginselen..."
    },
    {
      "number": "4",
      "content_hash": -2234567893,
      "text_preview": "Elke betrokkene heeft het recht..."
    },
    {
      "number": "5",
      "content_hash": -2234567894,
      "text_preview": "De verwerkingsverantwoordelijke implementeert passende..."
    },
    {
      "number": "6",
      "content_hash": -2234567895,
      "text_preview": "De verwerkingsverantwoordelijke en de verwerker..."
    },
    {
      "number": "7",
      "content_hash": -2234567896,
      "text_preview": "Inbreuken op de bepalingen van deze wet..."
    },
    {
      "number": "8",
      "content_hash": -2234567897,
      "text_preview": "Deze wet treedt in werking..."
    }
  ],
  "metadata": {
    "total_articles": 8,
    "chapters": 4,
    "language": "nl",
    "fixture_version": "1.0",
    "created_at": "2025-01-28T12:00:00Z"
  }
}
EOF
    fi
    
    echo "✅ Created golden data fixture: $file_path"
}

# Main execution
echo "🚀 Creating test fixtures for NUMAC: $SAMPLE_NUMAC"

# Create HTML fixtures
create_sample_html_fr
create_sample_html_nl

# Create golden data fixtures
create_golden_data "fr"
create_golden_data "nl"

echo ""
echo "✅ All fixtures created successfully!"
echo ""
echo "📋 Summary:"
echo "  - French HTML: $HTML_DIR/${SAMPLE_NUMAC}.fr.html"
echo "  - Dutch HTML: $HTML_DIR/${SAMPLE_NUMAC}.nl.html"
echo "  - French Golden: $GOLDEN_DIR/${SAMPLE_NUMAC}.fr.json"
echo "  - Dutch Golden: $GOLDEN_DIR/${SAMPLE_NUMAC}.nl.json"
echo ""
echo "🧪 You can now run the golden tests:"
echo "  pytest tests/test_golden_2022A30600.py -v"
echo ""
echo "📝 Note: The golden data contains placeholder hashes."
echo "   Run the tests once to generate actual hashes, then update the golden files."
